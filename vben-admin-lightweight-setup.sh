#!/bin/bash

# 轻量级Vben Admin Apple容器可视化界面
# 解决内存不足问题，使用更简单的部署方式

echo "🍎 轻量级Apple容器可视化界面部署"
echo "=================================="

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装"
    exit 1
fi

# 创建轻量级项目
PROJECT_DIR="apple-container-lightweight"
echo "📁 创建轻量级项目: $PROJECT_DIR"

if [ -d "$PROJECT_DIR" ]; then
    echo "⚠️  目录已存在，正在清理..."
    rm -rf "$PROJECT_DIR"
fi

mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# 创建package.json
echo "📦 创建package.json..."
cat > package.json << 'EOF'
{
  "name": "apple-container-lightweight",
  "version": "1.0.0",
  "description": "轻量级Apple容器可视化界面",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.3.0",
    "ant-design-vue": "^4.0.0",
    "@ant-design/icons-vue": "^7.0.0",
    "axios": "^1.6.0",
    "echarts": "^5.4.0",
    "vue-echarts": "^6.6.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.5.0",
    "vite": "^5.0.0",
    "typescript": "^5.2.0",
    "@types/node": "^20.8.0"
  }
}
EOF

# 创建Vite配置
echo "⚙️ 创建Vite配置..."
cat > vite.config.ts << 'EOF'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3100,
    host: '0.0.0.0'
  }
})
EOF

# 创建TypeScript配置
echo "📝 创建TypeScript配置..."
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
EOF

cat > tsconfig.node.json << 'EOF'
{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}
EOF

# 创建项目结构
echo "📁 创建项目结构..."
mkdir -p src/components src/api src/utils public

# 创建主应用文件
echo "📄 创建主应用文件..."
cat > src/main.ts << 'EOF'
import { createApp } from 'vue'
import App from './App.vue'
import 'ant-design-vue/dist/reset.css'

const app = createApp(App)
app.mount('#app')
EOF

# 创建App.vue
cat > src/App.vue << 'EOF'
<template>
  <div id="app">
    <a-config-provider>
      <ContainerManagement />
    </a-config-provider>
  </div>
</template>

<script setup lang="ts">
import { ConfigProvider } from 'ant-design-vue'
import ContainerManagement from './components/ContainerManagement.vue'
</script>

<style>
#app {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
EOF

# 创建容器管理组件
cat > src/components/ContainerManagement.vue << 'EOF'
<template>
  <div class="container-management">
    <a-layout>
      <a-layout-header class="header">
        <div class="header-content">
          <h1><AppleOutlined /> Apple容器管理面板</h1>
          <p>现代化的容器管理界面</p>
        </div>
      </a-layout-header>
      
      <a-layout-content class="content">
        <!-- 统计卡片 -->
        <div class="stats-row">
          <a-card class="stats-card">
            <Statistic
              title="运行容器"
              :value="systemInfo.containerCount"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <ContainerOutlined />
              </template>
            </Statistic>
          </a-card>
          
          <a-card class="stats-card">
            <Statistic
              title="可用镜像"
              :value="systemInfo.imageCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <PictureOutlined />
              </template>
            </Statistic>
          </a-card>
          
          <a-card class="stats-card">
            <Statistic
              title="系统版本"
              :value="systemInfo.version"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <SettingOutlined />
              </template>
            </Statistic>
          </a-card>
        </div>

        <!-- 容器列表 -->
        <a-card title="容器列表" class="container-card">
          <a-table
            :columns="containerColumns"
            :data-source="containerList"
            :loading="loading"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button size="small" @click="viewLogs(record)">
                    <FileTextOutlined /> 日志
                  </a-button>
                  <a-button size="small" type="primary" @click="startContainer(record)">
                    <PlayCircleOutlined /> 启动
                  </a-button>
                  <a-button size="small" danger @click="stopContainer(record)">
                    <StopOutlined /> 停止
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 镜像列表 -->
        <a-card title="镜像列表" class="image-card">
          <a-table
            :columns="imageColumns"
            :data-source="imageList"
            :loading="loading"
            :pagination="false"
            size="small"
          />
        </a-card>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  Layout, 
  Card, 
  Table, 
  Tag, 
  Button, 
  Space, 
  Statistic,
  ConfigProvider 
} from 'ant-design-vue'
import {
  AppleOutlined,
  ContainerOutlined,
  PictureOutlined,
  SettingOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  StopOutlined
} from '@ant-design/icons-vue'
import { getContainerList, getImageList, getSystemInfo } from '../api/container'

const { Header, Content } = Layout

const loading = ref(false)
const containerList = ref([])
const imageList = ref([])
const systemInfo = ref({
  containerCount: 0,
  imageCount: 0,
  version: 'Unknown'
})

const containerColumns = [
  {
    title: '容器名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '端口',
    dataIndex: 'ports',
    key: 'ports',
  },
  {
    title: '操作',
    key: 'action',
  },
]

const imageColumns = [
  {
    title: '镜像名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '标签',
    dataIndex: 'tag',
    key: 'tag',
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
  },
]

const getStatusColor = (status: string) => {
  const colorMap = {
    running: 'green',
    stopped: 'red',
    paused: 'orange',
  }
  return colorMap[status] || 'default'
}

const loadData = async () => {
  loading.value = true
  try {
    const [containers, images, system] = await Promise.all([
      getContainerList(),
      getImageList(),
      getSystemInfo(),
    ])
    containerList.value = containers
    imageList.value = images
    systemInfo.value = system
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const viewLogs = (container: any) => {
  console.log('查看日志:', container.name)
}

const startContainer = (container: any) => {
  console.log('启动容器:', container.name)
}

const stopContainer = (container: any) => {
  console.log('停止容器:', container.name)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.container-management {
  min-height: 100vh;
  background: #f0f2f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 50px;
  height: auto;
  line-height: 1.5;
}

.header-content {
  text-align: center;
  color: white;
  padding: 20px 0;
}

.header-content h1 {
  margin: 0;
  font-size: 2em;
  font-weight: 700;
}

.header-content p {
  margin: 10px 0 0 0;
  opacity: 0.9;
}

.content {
  padding: 24px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  text-align: center;
}

.container-card,
.image-card {
  margin-bottom: 24px;
}

.ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ant-card-head-title {
  color: white;
}
</style>
EOF

# 创建API文件
cat > src/api/container.ts << 'EOF'
import axios from 'axios'

const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000,
})

export const getContainerList = () => {
  return api.get('/containers').then(res => res.data)
}

export const getImageList = () => {
  return api.get('/images').then(res => res.data)
}

export const getSystemInfo = () => {
  return api.get('/system').then(res => res.data)
}
EOF

# 创建HTML模板
cat > index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🍎 Apple容器管理面板</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
EOF

# 安装依赖
echo "📦 安装依赖..."
npm install

# 创建启动脚本
echo "📝 创建启动脚本..."
cat > start-lightweight.sh << 'EOF'
#!/bin/bash

echo "🍎 轻量级Apple容器可视化界面启动"
echo "================================"

# 检查Python依赖
echo "📦 检查Python依赖..."
python3 -c "import flask" 2>/dev/null || {
    echo "📦 安装Flask..."
    python3 -m pip install --break-system-packages flask flask-cors
}

# 启动后端API服务
echo "🚀 启动后端API服务..."
cd ../server
python3 app.py &
API_PID=$!
cd ../apple-container-lightweight

# 等待API服务启动
sleep 3

# 启动前端服务
echo "🚀 启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ 服务启动完成！"
echo "📡 后端API: http://localhost:5000"
echo "🌐 前端界面: http://localhost:3100"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $API_PID $FRONTEND_PID 2>/dev/null; exit" INT
wait
EOF

chmod +x start-lightweight.sh

echo ""
echo "✅ 轻量级Apple容器可视化界面部署完成！"
echo ""
echo "📁 项目目录: $PROJECT_DIR"
echo "🚀 启动命令: cd $PROJECT_DIR && ./start-lightweight.sh"
echo ""
echo "🎯 特点:"
echo "   • 轻量级部署，解决内存问题"
echo "   • 基于Vue 3 + Ant Design Vue"
echo "   • 现代化UI设计"
echo "   • 完整的容器管理功能"
echo ""
echo "🌐 访问地址:"
echo "   • 前端界面: http://localhost:3100"
echo "   • 后端API: http://localhost:5000" 