{"mcpServers": {"sequential-thinking-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "逻辑思维增强MCP - 动态反思问题解决", "enabled": true}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "memory.json"}, "description": "持久化记忆存储MCP", "enabled": true}, "filesystem-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ROOT_PATH": "/Users/<USER>/未命名文件夹"}, "description": "文件系统操作MCP", "enabled": true}, "time-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "description": "时间和时区转换MCP", "enabled": true}, "wechat-service-mcp": {"command": "node", "args": ["/Users/<USER>/.cursor/servers/wechat-service-mcp.js"], "env": {"WECHAT_APPID": "wxa87c0181443d142f", "WECHAT_APPSECRET": "f239b47916a62c57fef6392957b65bf1"}, "description": "微信服务号MCP - 认证服务号完整功能（全局版本）", "enabled": true}, "wechat-oa-mcp": {"command": "pipx", "args": ["run", "wechat-oa-mcp"], "description": "微信公众号MCP - pipx版本", "enabled": true}, "aliyun-bailian": {"command": "node", "args": ["/Users/<USER>/.cursor/servers/aliyun-bailian-mcp.js"], "env": {"ALIYUN_BAILIAN_API_KEY": "${ALIYUN_BAILIAN_API_KEY}", "ALIYUN_ACCESS_KEY_ID": "${ALIYUN_ACCESS_KEY_ID}", "ALIYUN_ACCESS_KEY_SECRET": "${ALIYUN_ACCESS_KEY_SECRET}"}, "description": "阿里云百炼MCP - 通义千问AI模型服务（全局版本）", "enabled": true}, "test-service-mcp": {"command": "node", "args": ["/Users/<USER>/.cursor/servers/test_service_mcp.js"], "description": "测试服务MCP - 本地测试工具", "enabled": false}, "advanced-info-check": {"command": "node", "args": ["/Users/<USER>/.cursor/servers/advanced-info-check.js"], "description": "高级信息检查MCP - 系统信息分析", "enabled": true}, "wechat-article-push": {"command": "node", "args": ["/Users/<USER>/.cursor/servers/wechat-article-push.js"], "description": "微信文章推送MCP - 内容发布工具", "enabled": true}, "notion-official": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-notion"], "env": {"NOTION_API_TOKEN": "${NOTION_TOKEN}"}, "description": "Notion官方MCP服务 - 知识库管理", "enabled": true}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}, "description": "GitHub仓库管理MCP", "enabled": true}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "${BRAVE_API_KEY}"}, "description": "Brave搜索引擎MCP - 网络和本地搜索服务", "enabled": true}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {"DB_PATH": "/Users/<USER>/未命名文件夹/data/mcp.db"}, "description": "SQLite数据库MCP - 数据库交互和商业智能", "enabled": true}, "playwright-mcp": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {"PLAYWRIGHT_HEADLESS": "true"}, "description": "Playwright自动化测试MCP", "enabled": true}, "playwright-official": {"command": "npx", "args": ["-y", "@playwright/mcp"], "description": "Playwright官方MCP服务器", "enabled": true}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "description": "Puppeteer浏览器自动化MCP - 网页抓取和自动化", "enabled": true}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "description": "HTTP客户端MCP - 外部API请求工具", "enabled": true}, "git-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"], "description": "Git版本控制MCP", "enabled": true}, "docker": {"command": "uvx", "args": ["docker-mcp"], "description": "Docker容器管理MCP", "enabled": true}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"DEFAULT_MINIMUM_TOKENS": "10000"}, "description": "Context7智能上下文管理MCP", "enabled": true}, "browser-tools-mcp": {"command": "npx", "args": ["-y", "@agentdeskai/browser-tools-mcp"], "description": "浏览器工具MCP - Agent桌面AI", "enabled": true}, "browser-mcp": {"command": "npx", "args": ["-y", "@browsermcp/mcp"], "description": "浏览器MCP - 网页操作工具", "enabled": true}, "task-manager": {"command": "npx", "args": ["-y", "@kazuph/mcp-taskmanager"], "description": "任务管理MCP服务", "enabled": true}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "${POSTGRES_CONNECTION_STRING}"}, "description": "PostgreSQL数据库MCP", "enabled": false}, "mysql": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mysql"], "env": {"MYSQL_CONNECTION_STRING": "${MYSQL_CONNECTION_STRING}"}, "description": "MySQL数据库MCP", "enabled": false}, "redis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-redis"], "env": {"REDIS_URL": "${REDIS_URL}"}, "description": "Redis缓存数据库MCP", "enabled": false}, "mongodb": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mongodb"], "env": {"MONGODB_URI": "${MONGODB_URI}"}, "description": "MongoDB数据库MCP", "enabled": false}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}"}, "description": "Slack通信MCP", "enabled": false}, "discord": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-discord"], "env": {"DISCORD_BOT_TOKEN": "${DISCORD_BOT_TOKEN}"}, "description": "Discord通信MCP", "enabled": false}, "telegram": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-telegram"], "env": {"TELEGRAM_BOT_TOKEN": "${TELEGRAM_BOT_TOKEN}"}, "description": "Telegram机器人MCP", "enabled": false}, "google-drive": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdrive"], "env": {"GDRIVE_CLIENT_ID": "${GDRIVE_CLIENT_ID}", "GDRIVE_CLIENT_SECRET": "${GDRIVE_CLIENT_SECRET}", "GDRIVE_REFRESH_TOKEN": "${GDRIVE_REFRESH_TOKEN}"}, "description": "Google Drive文件管理MCP", "enabled": false}, "google-sheets": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gsheets"], "env": {"GOOGLE_SHEETS_API_KEY": "${GOOGLE_SHEETS_API_KEY}"}, "description": "Google Sheets表格MCP", "enabled": false}, "google-maps": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gmaps"], "env": {"GOOGLE_MAPS_API_KEY": "${GOOGLE_MAPS_API_KEY}"}, "description": "Google Maps地图MCP", "enabled": false}, "youtube-transcript": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-youtube-transcript"], "description": "YouTube转录MCP", "enabled": true}, "youtube-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-youtube"], "env": {"YOUTUBE_API_KEY": "${YOUTUBE_API_KEY}"}, "description": "YouTube搜索MCP", "enabled": false}, "aws-kb": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws-kb"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_REGION": "${AWS_REGION}"}, "description": "AWS知识库MCP", "enabled": false}, "aws-s3": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-s3"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_REGION": "${AWS_REGION}"}, "description": "AWS S3存储MCP", "enabled": false}, "anthropic": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-anthropic"], "env": {"ANTHROPIC_API_KEY": "${ANTHROPIC_API_KEY}"}, "description": "Anthropic AI MCP", "enabled": false}, "openai": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-openai"], "env": {"OPENAI_API_KEY": "${OPENAI_API_KEY}"}, "description": "OpenAI GPT MCP", "enabled": false}, "obsidian": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-obsidian"], "env": {"OBSIDIAN_VAULT_PATH": "${OBSIDIAN_VAULT_PATH}"}, "description": "Obsidian笔记MCP", "enabled": false}, "linear": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-linear"], "env": {"LINEAR_API_KEY": "${LINEAR_API_KEY}"}, "description": "Linear项目管理MCP", "enabled": false}, "jira": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-jira"], "env": {"JIRA_HOST": "${JIRA_HOST}", "JIRA_USERNAME": "${JIRA_USERNAME}", "JIRA_API_TOKEN": "${JIRA_API_TOKEN}"}, "description": "Jira项目管理MCP", "enabled": false}, "trello": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-trello"], "env": {"TRELLO_API_KEY": "${TRELLO_API_KEY}", "TRELLO_TOKEN": "${TRELLO_TOKEN}"}, "description": "Trello看板MCP", "enabled": false}, "asana": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-asana"], "env": {"ASANA_ACCESS_TOKEN": "${ASANA_ACCESS_TOKEN}"}, "description": "Asana任务管理MCP", "enabled": false}, "calendar": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-calendar"], "env": {"GOOGLE_CALENDAR_API_KEY": "${GOOGLE_CALENDAR_API_KEY}"}, "description": "日历管理MCP", "enabled": false}, "email": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-email"], "env": {"EMAIL_SMTP_HOST": "${EMAIL_SMTP_HOST}", "EMAIL_SMTP_PORT": "${EMAIL_SMTP_PORT}", "EMAIL_USERNAME": "${EMAIL_USERNAME}", "EMAIL_PASSWORD": "${EMAIL_PASSWORD}"}, "description": "邮件发送MCP", "enabled": false}, "weather": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-weather"], "env": {"WEATHER_API_KEY": "${WEATHER_API_KEY}"}, "description": "天气查询MCP", "enabled": false}, "news": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-news"], "env": {"NEWS_API_KEY": "${NEWS_API_KEY}"}, "description": "新闻获取MCP", "enabled": false}, "currency": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-currency"], "env": {"CURRENCY_API_KEY": "${CURRENCY_API_KEY}"}, "description": "汇率转换MCP", "enabled": false}, "stock": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-stock"], "env": {"STOCK_API_KEY": "${STOCK_API_KEY}"}, "description": "股票信息MCP", "enabled": false}, "crypto": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-crypto"], "env": {"CRYPTO_API_KEY": "${CRYPTO_API_KEY}"}, "description": "加密货币MCP", "enabled": false}, "figma": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-figma"], "env": {"FIGMA_ACCESS_TOKEN": "${FIGMA_ACCESS_TOKEN}", "FIGMA_FILE_KEY": "${FIGMA_FILE_KEY}"}, "description": "Figma设计协作MCP - 设计稿管理和代码生成", "enabled": true}}}