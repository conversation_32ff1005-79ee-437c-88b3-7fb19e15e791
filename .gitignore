# Apple容器化项目 .gitignore

# ===== 操作系统文件 =====
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.tmp
*.temp
*.log
*.swp
*.swo
*~

# ===== Python =====
# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pytest
.pytest_cache/
.coverage
htmlcov/

# ===== Go =====
# 二进制文件
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件
*.test

# 输出目录
/mcp-proxy/build/
/mcp-proxy/dist/

# Go工作区文件
go.work

# 依赖目录
vendor/

# ===== Node.js (如果有) =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# ===== 容器相关 =====
# Docker
.dockerignore

# Apple容器数据
*.container
*.vm

# ===== 配置和敏感信息 =====
# 环境变量文件
.env.local
.env.development.local
.env.test.local
.env.production.local

# 配置文件中的敏感信息
*secret*
*password*
*key*
*.pem
*.key
*.crt
*.p12

# ===== 数据和日志 =====
# 日志文件
/data/logs/
*.log
logs/

# 缓存文件
/data/cache/
cache/
.cache/

# 临时文件
/data/temp/
temp/
tmp/
*.tmp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# ===== IDE和编辑器 =====
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 项目特定 =====
# MCP配置备份
/mcp-tools/*backup*.json
/mcp-tools/temp-*.json

# Apple容器运行时文件
/apple-containers/*.pid
/apple-containers/runtime/

# 构建产物
/build/
/dist/
/out/

# 测试覆盖率报告
coverage/
.nyc_output/

# 性能分析文件
*.prof
*.pprof

# ===== 归档和备份 =====
# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 备份文件
*.bak
*.backup
*.old

# ===== 其他 =====
# 系统生成的文件
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 网络文件
.netrwhist

# 标签文件
tags
TAGS

# 本地配置文件
local.json
local.yaml
local.yml
