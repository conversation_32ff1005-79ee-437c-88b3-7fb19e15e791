#!/bin/bash

# Docker镜像清理脚本
# 针对MCP、N8N、Puppeteer日常使用优化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行或无法访问"
        exit 1
    fi
}

# 显示当前镜像统计
show_current_status() {
    echo "📊 当前Docker镜像状态"
    echo "===================="
    
    local total_images=$(docker images -q | wc -l)
    local total_size=$(docker images --format "table {{.Size}}" | grep -v SIZE | sed 's/GB//' | sed 's/MB//' | awk '{sum+=$1} END {print sum}')
    local dangling_images=$(docker images -f "dangling=true" -q | wc -l)
    
    echo "• 总镜像数: $total_images"
    echo "• 悬空镜像: $dangling_images"
    echo "• 估算总大小: ${total_size}MB"
    echo ""
}

# 分析你的核心服务
analyze_core_services() {
    echo "🎯 你的核心服务分析"
    echo "=================="
    
    local services=(
        "n8nio/n8n:工作流自动化:高优先级:必须保留"
        "mcp/puppeteer:网页爬虫:高优先级:必须保留"
        "ghcr.io/tbxark/mcp-proxy:MCP代理:高优先级:必须保留"
        "redis:缓存服务:中优先级:建议保留"
        "node:基础环境:中优先级:建议保留"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name desc priority action <<< "$service"
        
        if docker images | grep -q "$name"; then
            status="${GREEN}✅ 已安装${NC}"
        else
            status="${RED}❌ 未安装${NC}"
        fi
        
        case $priority in
            "高优先级") color=$RED ;;
            "中优先级") color=$YELLOW ;;
            *) color=$GREEN ;;
        esac
        
        echo -e "${color}• $name${NC}: $desc ($priority) - $action $status"
    done
    echo ""
}

# 识别重复和无用镜像
identify_cleanup_targets() {
    echo "🗑️ 可清理的镜像"
    echo "==============="
    
    # 重复的Playwright镜像
    local playwright_images=$(docker images | grep "mcp/playwright" | wc -l)
    if [ "$playwright_images" -gt 1 ]; then
        log_warning "发现 $playwright_images 个Playwright镜像（建议只保留1个）"
        docker images | grep "mcp/playwright" | head -n -1 | awk '{print $3}'
    fi
    
    # 悬空镜像
    local dangling=$(docker images -f "dangling=true" -q | wc -l)
    if [ "$dangling" -gt 0 ]; then
        log_warning "发现 $dangling 个悬空镜像"
    fi
    
    # 无标签镜像
    local none_images=$(docker images | grep "<none>" | wc -l)
    if [ "$none_images" -gt 0 ]; then
        log_warning "发现 $none_images 个无标签镜像"
    fi
    
    echo ""
}

# 安全清理 - 保留核心服务
safe_cleanup() {
    log_info "开始安全清理..."
    
    # 定义需要保留的镜像模式
    local keep_patterns=(
        "n8nio/n8n"
        "ghcr.io/tbxark/mcp-proxy"
        "redis.*alpine"
        "node.*alpine"
    )
    
    # 1. 清理悬空镜像
    log_info "清理悬空镜像..."
    docker image prune -f
    
    # 2. 清理无用的容器
    log_info "清理停止的容器..."
    docker container prune -f
    
    # 3. 清理网络
    log_info "清理未使用的网络..."
    docker network prune -f
    
    # 4. 清理卷（小心，会删除数据）
    read -p "是否清理未使用的数据卷？(y/N): " clean_volumes
    if [[ $clean_volumes =~ ^[Yy]$ ]]; then
        log_warning "清理数据卷..."
        docker volume prune -f
    fi
    
    log_success "安全清理完成！"
}

# 深度清理 - 清理所有无用镜像
deep_cleanup() {
    log_warning "准备进行深度清理..."
    echo "这将删除所有未使用的镜像、容器、网络和卷"
    read -p "确定要继续吗？(y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        log_info "执行深度清理..."
        docker system prune -a -f --volumes
        log_success "深度清理完成！"
    else
        log_info "已取消深度清理"
    fi
}

# 重新拉取核心镜像
update_core_images() {
    log_info "更新核心镜像..."
    
    local core_images=(
        "n8nio/n8n:latest"
        "ghcr.io/tbxark/mcp-proxy:latest"
        "redis:7-alpine"
        "node:18-alpine"
    )
    
    for image in "${core_images[@]}"; do
        log_info "更新 $image..."
        docker pull "$image"
    done
    
    log_success "核心镜像更新完成！"
}

# 显示优化建议
show_optimization_tips() {
    echo "💡 优化建议"
    echo "==========="
    echo "1. 定期运行 'docker system prune -f' 清理临时文件"
    echo "2. 使用 'docker images --format \"table {{.Repository}}:{{.Tag}}\t{{.Size}}\"' 监控镜像大小"
    echo "3. 考虑使用 .dockerignore 文件减少构建上下文"
    echo "4. 使用多阶段构建减少最终镜像大小"
    echo "5. 定期更新基础镜像获取安全补丁"
    echo ""
    
    echo "🎯 针对你的使用场景："
    echo "• N8N: 建议使用持久化卷存储工作流数据"
    echo "• Puppeteer: 可以使用 browserless/chrome 替代自建镜像"
    echo "• MCP: 配置文件外部挂载，便于管理"
    echo ""
}

# 创建优化的docker-compose
create_optimized_compose() {
    log_info "创建优化的docker-compose.yml..."
    
    cat > docker-compose-optimized.yml << 'EOF'
version: '3.8'

services:
  # N8N工作流引擎
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n-backup:/backup
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=changeme
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - WEBHOOK_URL=http://localhost:5678/
    restart: unless-stopped
    networks:
      - mcp_network

  # MCP代理服务
  mcp-proxy:
    image: ghcr.io/tbxark/mcp-proxy:latest
    container_name: mcp-proxy
    ports:
      - "9090:9090"
    volumes:
      - ./mcp-proxy-config.json:/config/config.json:ro
    restart: unless-stopped
    networks:
      - mcp_network

  # Puppeteer爬虫服务
  puppeteer:
    image: browserless/chrome:latest
    container_name: puppeteer
    ports:
      - "3000:3000"
    environment:
      - MAX_CONCURRENT_SESSIONS=3
      - CONNECTION_TIMEOUT=60000
      - MAX_QUEUE_LENGTH=10
    restart: unless-stopped
    networks:
      - mcp_network
    shm_size: '2gb'

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - mcp_network

volumes:
  n8n_data:
  redis_data:

networks:
  mcp_network:
    driver: bridge
EOF

    log_success "已创建 docker-compose-optimized.yml"
    echo "使用命令启动: docker-compose -f docker-compose-optimized.yml up -d"
}

# 主菜单
show_menu() {
    echo "🐳 Docker镜像优化工具"
    echo "===================="
    echo "1. 显示当前状态"
    echo "2. 分析核心服务"
    echo "3. 识别清理目标"
    echo "4. 安全清理（推荐）"
    echo "5. 深度清理（危险）"
    echo "6. 更新核心镜像"
    echo "7. 创建优化配置"
    echo "8. 显示优化建议"
    echo "9. 退出"
    echo ""
}

# 主逻辑
main() {
    check_docker
    
    if [ $# -eq 0 ]; then
        while true; do
            show_menu
            read -p "请选择操作 (1-9): " choice
            echo ""
            
            case $choice in
                1) show_current_status ;;
                2) analyze_core_services ;;
                3) identify_cleanup_targets ;;
                4) safe_cleanup ;;
                5) deep_cleanup ;;
                6) update_core_images ;;
                7) create_optimized_compose ;;
                8) show_optimization_tips ;;
                9) log_info "再见！"; exit 0 ;;
                *) log_error "无效选择，请重试" ;;
            esac
            echo ""
            read -p "按回车键继续..."
            clear
        done
    else
        case $1 in
            "status") show_current_status ;;
            "analyze") analyze_core_services ;;
            "cleanup") safe_cleanup ;;
            "deep-cleanup") deep_cleanup ;;
            "update") update_core_images ;;
            "optimize") create_optimized_compose ;;
            "tips") show_optimization_tips ;;
            *) echo "用法: $0 [status|analyze|cleanup|deep-cleanup|update|optimize|tips]" ;;
        esac
    fi
}

main "$@" 