# 🍎 Apple容器化项目集合

**一站式Apple原生容器管理和MCP工具整合方案**

## 📁 项目结构

```
📦 Apple容器化项目/
├── 🍎 apple-containers/          # Apple容器管理工具
├── 🔧 mcp-tools/                 # MCP聚合器和工具
├── 📚 docs/                      # 项目文档
├── 🛠️ scripts/                   # 实用脚本
├── ⚙️ configs/                   # 配置文件
├── 📁 archive/                   # 归档文件
└── 📄 README.md                  # 项目说明
```

## 🍎 Apple容器管理 (`apple-containers/`)

### 🌟 主要功能
- **🌐 Web监控面板**: 现代化浏览器可视化管理界面
  - 实时容器状态监控和性能指标
  - 容器生命周期管理（启动/停止/重启/删除）
  - 实时日志查看和系统信息展示
  - 响应式设计，支持移动端访问
- **💻 终端仪表板**: 命令行实时监控界面
- **🔧 容器管理器**: 一键启停和状态查看脚本
- **📦 迁移工具**: 从Docker平滑迁移到Apple容器

### 🚀 快速开始

#### 方法1: 一键启动器 (推荐)
```bash
cd apple-containers
./start-apple-container-ui.sh
# 选择 "1) 🌐 Web监控面板"
```

#### 方法2: 直接启动Web面板
```bash
cd apple-containers
python3 apple-container-web-panel.py
# 访问: http://localhost:5000
```

#### 方法3: 容器状态管理
```bash
./apple-container-manager.sh status    # 查看状态
./apple-container-manager.sh start     # 启动所有服务
./apple-container-manager.sh stop      # 停止所有服务
```

### 🎯 Web面板新功能 (v2.0)
- **🔄 实时操作**: 直接在Web界面执行容器操作
- **📊 性能监控**: CPU和内存使用率实时显示
- **📋 日志查看**: 模态框实时日志流查看
- **⚡ 快捷操作**: 批量管理和快捷键支持
- **🚨 健康监控**: 容器健康状态和系统告警
- **📱 响应式**: 完美支持桌面和移动设备

### 📦 包含文件
- `apple-container-web-panel.py` - **Web监控面板 v2.0** (🌟强烈推荐)
- `apple-container-dashboard.py` - 终端仪表板
- `apple-container-manager.sh` - 容器管理脚本
- `start-apple-container-ui.sh` - UI启动器
- `migrate-to-apple.sh` - Docker迁移工具

## 🔧 MCP工具集 (`mcp-tools/`)

### 🎯 解决的问题
- **工具数量限制**: IDE最多40个工具，MCP服务器有100+
- **智能切换**: 根据场景自动切换MCP服务器组合
- **聚合管理**: 统一管理多个MCP服务器

### 🌟 可用场景
1. **通用开发**: 思维、内存、文件系统、时间 (14个工具)
2. **Web开发**: + Playwright、搜索 (21个工具)  
3. **GitHub协作**: + GitHub (19个工具)
4. **知识管理**: + Notion、SQLite (22个工具)
5. **全功能**: 所有服务器 (34个工具)

### 🚀 快速使用
```bash
cd mcp-tools

# 查看可用场景
python3 mcp-switcher.py list

# 切换到Web开发场景
python3 mcp-switcher.py switch "Web开发"

# 根据关键词自动切换
python3 mcp-switcher.py auto 浏览器 网页
```

## 📚 文档中心 (`docs/`)

### 📖 主要文档
- `Apple容器化完整指南.md` - Apple容器使用指南
- `迁移完成报告.md` - Docker迁移详细报告
- `MCP-聚合器使用指南.md` - MCP工具完整说明
- `Docker镜像清理和优化方案.md` - Docker资源优化

## 🏆 项目亮点

### ✨ Apple容器优势
```bash
✅ 启动速度: 5秒内 (vs Docker 60秒+)
✅ 内存节约: 释放1.5GB+ Docker守护进程  
✅ 网络简化: 独立IP，无需端口映射
✅ 系统集成: 原生macOS支持，更稳定
```

### 🔥 已迁移服务
```bash
🔧 N8N工作流: http://************:5678 (admin/changeme)
💾 Redis缓存: ************:6379
📡 MCP代理: localhost:9090
```

### 📊 性能提升统计
| 项目 | Docker Desktop | Apple容器 | 提升 |
|------|--------|-------|------|
| 启动速度 | 60秒+ | 3秒 | 20x |
| 内存使用 | 2.5GB+ | 0.8GB | 1.7GB↓ |
| 磁盘占用 | 8.2GB | 2.9GB | 5.3GB↓ |

## 🛠️ 系统要求

- **系统**: macOS 26+ (Sequoia/Sonoma 16+)
- **Python**: 3.8+
- **依赖**: Flask (Web界面), Rich (终端美化)

## 🚀 一键开始

### 1. 启动Apple容器服务
```bash
# 检查容器状态
./apple-containers/apple-container-manager.sh status

# 启动Web监控面板
./apple-containers/start-apple-container-ui.sh
```

### 2. 配置MCP工具
```bash
# 查看MCP场景
./mcp-tools/mcp-switcher.py list

# 设置开发环境
./mcp-tools/mcp-switcher.py switch "通用开发"
```

## 📞 支持与反馈

- **问题反馈**: 检查 `docs/` 目录下的详细文档
- **快速问题**: 查看 `迁移完成报告.md` 的故障排除部分
- **高级配置**: 参考 `Apple容器化完整指南.md`

## 🎯 下一步计划

- [ ] 添加容器日志聚合功能
- [ ] 支持更多MCP服务器
- [ ] 开发移动端监控应用
- [ ] 集成Grafana监控仪表板

---

**🎉 恭喜！你现在拥有了最先进的Apple原生容器管理环境！**

*享受超高效、低资源消耗的开发体验吧！* 🚀✨ 