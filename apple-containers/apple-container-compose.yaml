# Apple Container 配置文件
# 替代docker-compose.yml

services:
  # N8N工作流引擎
  n8n:
    image: n8nio/n8n:latest
    name: apple-n8n
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=changeme
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
    # Apple容器自动分配IP，无需端口映射
    # 通过容器IP直接访问，如 http://************:5678

  # MCP代理服务
  mcp-proxy:
    image: ghcr.io/tbxark/mcp-proxy:latest
    name: apple-mcp-proxy
    # 配置文件需要挂载到容器内

  # Redis缓存
  redis:
    image: redis:alpine
    name: apple-redis
    # 已经运行中: ************

  # Puppeteer爬虫服务 
  puppeteer:
    image: browserless/chrome:latest
    name: apple-puppeteer
    environment:
      - MAX_CONCURRENT_SESSIONS=3
      - CONNECTION_TIMEOUT=60000

# 启动命令示例:
# container run -d --name apple-n8n -e N8N_BASIC_AUTH_ACTIVE=true n8nio/n8n:latest 