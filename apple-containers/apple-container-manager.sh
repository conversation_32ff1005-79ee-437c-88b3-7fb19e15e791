#!/bin/bash

# Apple容器快速管理脚本

case "$1" in
    "start")
        echo "🚀 启动所有Apple容器服务..."
        container start apple-n8n-fixed apple-redis 2>/dev/null || echo "容器已在运行"
        ;;
    "stop")
        echo "⏹️ 停止所有Apple容器服务..."
        container stop apple-n8n-fixed apple-redis 2>/dev/null
        ;;
    "status")
        echo "📊 Apple容器服务状态:"
        container list
        echo ""
        echo "🔗 服务访问地址:"
        container list | grep apple-n8n-fixed | awk '{print "🔧 N8N工作流: http://"$NF":5678 (admin/changeme)"}'
        container list | grep apple-redis | awk '{print "💾 Redis缓存: "$NF":6379"}'
        ;;
    "logs")
        if [ "$2" = "n8n" ]; then
            container logs apple-n8n-fixed
        elif [ "$2" = "redis" ]; then
            container logs apple-redis
        else
            echo "用法: $0 logs [n8n|redis]"
        fi
        ;;
    "restart")
        echo "🔄 重启Apple容器服务..."
        container stop apple-n8n-fixed apple-redis 2>/dev/null
        sleep 2
        container start apple-n8n-fixed apple-redis
        ;;
    "update")
        echo "🔄 更新N8N到最新版本..."
        container stop apple-n8n-fixed
        container delete apple-n8n-fixed
        container images pull n8nio/n8n:latest
        container run -d --name apple-n8n-fixed \
            -e N8N_BASIC_AUTH_ACTIVE=true \
            -e N8N_BASIC_AUTH_USER=admin \
            -e N8N_BASIC_AUTH_PASSWORD=changeme \
            -e N8N_HOST=0.0.0.0 \
            -e N8N_PORT=5678 \
            -e N8N_SECURE_COOKIE=false \
            n8nio/n8n:latest
        echo "✅ N8N更新完成"
        ;;
    *)
        echo "Apple容器管理工具"
        echo "用法: $0 {start|stop|status|logs|restart|update}"
        echo ""
        echo "命令:"
        echo "  start   - 启动所有服务"
        echo "  stop    - 停止所有服务"
        echo "  status  - 查看服务状态和访问地址"
        echo "  logs    - 查看日志 (需指定服务名: n8n|redis)"
        echo "  restart - 重启所有服务"
        echo "  update  - 更新N8N到最新版本"
        ;;
esac 