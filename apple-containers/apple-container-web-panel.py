#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Apple容器Web监控面板
功能描述：提供完整的Web界面来监控和管理Apple容器
作者：项目团队
创建时间：2024-07-26
版本：2.0 - 增强版
"""
import subprocess
import json
import re
import os
import sys
from flask import Flask, render_template_string, jsonify, request
from datetime import datetime, timedelta
import threading
import time
import logging
from collections import defaultdict, deque

app = Flask(__name__)

# 配置日志
log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'data', 'logs')
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'web-panel.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局配置
CONFIG = {
    'refresh_interval': 30,  # 自动刷新间隔（秒）
    'max_log_lines': 1000,   # 最大日志行数
    'performance_history_size': 100,  # 性能历史数据大小
    'container_manager_script': './apple-container-manager.sh'
}

# 性能数据存储
performance_data = defaultdict(lambda: deque(maxlen=CONFIG['performance_history_size']))
system_alerts = []

def run_command(cmd, timeout=10):
    """执行命令并返回结果"""
    try:
        logger.info(f"执行命令: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode != 0:
            logger.warning(f"命令执行失败: {cmd}, 错误: {result.stderr}")
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except subprocess.TimeoutExpired:
        logger.error(f"命令超时: {cmd}")
        return "", "Command timeout", 1
    except Exception as e:
        logger.error(f"命令执行异常: {cmd}, 错误: {str(e)}")
        return "", str(e), 1

def run_manager_script(action, *args):
    """执行管理脚本"""
    # 获取脚本的绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    script_path = os.path.join(script_dir, 'apple-container-manager.sh')

    if not os.path.exists(script_path):
        logger.error(f"管理脚本不存在: {script_path}")
        return "", f"管理脚本不存在: {script_path}", 1

    # 确保脚本有执行权限
    try:
        os.chmod(script_path, 0o755)
    except Exception as e:
        logger.warning(f"设置脚本权限失败: {e}")

    cmd = f"bash {script_path} {action}"
    if args:
        cmd += " " + " ".join(args)

    logger.info(f"执行管理脚本: {cmd}")
    return run_command(cmd, timeout=30)

def get_container_performance(container_id):
    """获取容器性能数据"""
    # 这里可以扩展获取实际的性能数据
    # 目前返回模拟数据
    import random
    return {
        'cpu_percent': round(random.uniform(0, 100), 2),
        'memory_percent': round(random.uniform(0, 100), 2),
        'network_io': {
            'bytes_sent': random.randint(1000, 10000),
            'bytes_recv': random.randint(1000, 10000)
        },
        'timestamp': datetime.now().isoformat()
    }

def get_container_list():
    """获取容器列表"""
    stdout, stderr, code = run_command("container list")
    if code != 0:
        logger.error(f"获取容器列表失败: {stderr}")
        return []

    containers = []
    lines = stdout.split('\n')[1:]  # 跳过标题行
    for line in lines:
        if line.strip():
            parts = line.split()
            if len(parts) >= 6:
                container_id = parts[0]
                container_info = {
                    'id': container_id,
                    'image': parts[1],
                    'os': parts[2],
                    'arch': parts[3],
                    'state': parts[4],
                    'addr': parts[5] if len(parts) > 5 else 'N/A',
                    'performance': get_container_performance(container_id),
                    'uptime': get_container_uptime(container_id),
                    'health': get_container_health(container_id)
                }
                containers.append(container_info)

                # 存储性能历史数据
                perf_data = container_info['performance']
                performance_data[container_id].append({
                    'timestamp': perf_data['timestamp'],
                    'cpu': perf_data['cpu_percent'],
                    'memory': perf_data['memory_percent']
                })

    return containers

def get_container_uptime(container_id):
    """获取容器运行时间"""
    # 这里可以实现实际的运行时间获取逻辑
    # 目前返回模拟数据
    import random
    hours = random.randint(0, 72)
    minutes = random.randint(0, 59)
    return f"{hours}h {minutes}m"

def get_container_health(container_id):
    """获取容器健康状态"""
    # 这里可以实现实际的健康检查逻辑
    # 目前返回模拟数据
    import random
    statuses = ['healthy', 'warning', 'critical']
    return random.choice(statuses)

def get_images_list():
    """获取镜像列表"""
    stdout, stderr, code = run_command("container images list")
    if code != 0:
        return []
    
    images = []
    lines = stdout.split('\n')[1:]  # 跳过标题行
    for line in lines:
        if line.strip() and not line.startswith('REPOSITORY'):
            parts = line.split()
            if len(parts) >= 4:
                images.append({
                    'repository': parts[0],
                    'tag': parts[1],
                    'image_id': parts[2],
                    'size': parts[3] if len(parts) > 3 else 'N/A'
                })
    return images

def get_system_info():
    """获取系统信息"""
    try:
        # 获取容器系统状态
        stdout, stderr, code = run_command("container system status")
        system_status = "running" if code == 0 else "stopped"

        # 获取资源使用情况
        containers = get_container_list()
        running_containers = len([c for c in containers if c['state'] == 'running'])

        # 检查管理脚本状态
        script_dir = os.path.dirname(os.path.abspath(__file__))
        script_path = os.path.join(script_dir, 'apple-container-manager.sh')
        script_available = os.path.exists(script_path)

        # 系统健康检查
        health_issues = []
        if system_status != "running":
            health_issues.append("容器系统未运行")
        if not script_available:
            health_issues.append("管理脚本不可用")

        # 检查容器健康状态
        unhealthy_containers = [c for c in containers if c.get('health') in ['warning', 'critical']]
        if unhealthy_containers:
            health_issues.append(f"{len(unhealthy_containers)}个容器状态异常")

        return {
            'system_status': system_status,
            'total_containers': len(containers),
            'running_containers': running_containers,
            'script_available': script_available,
            'health_issues': health_issues,
            'uptime': get_system_uptime(),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        return {
            'system_status': 'unknown',
            'total_containers': 0,
            'running_containers': 0,
            'script_available': False,
            'health_issues': ['系统信息获取失败'],
            'uptime': 'unknown',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

def get_system_uptime():
    """获取系统运行时间"""
    try:
        stdout, stderr, code = run_command("uptime")
        if code == 0:
            return stdout.strip()
        return "unknown"
    except Exception:
        return "unknown"

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍎 Apple容器监控面板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { 
            background: rgba(255,255,255,0.1); 
            backdrop-filter: blur(10px);
            border-radius: 20px; 
            padding: 30px; 
            margin-bottom: 30px; 
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .header h1 { color: white; font-size: 2.5em; margin-bottom: 10px; }
        .header p { color: rgba(255,255,255,0.8); font-size: 1.1em; }
        .status-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .status-card { 
            background: rgba(255,255,255,0.1); 
            backdrop-filter: blur(10px);
            border-radius: 15px; 
            padding: 25px; 
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .status-card:hover { transform: translateY(-5px); }
        .status-card h3 { color: white; margin-bottom: 10px; font-size: 1.2em; }
        .status-card .value { color: #4ade80; font-size: 2em; font-weight: bold; }
        .status-card .label { color: rgba(255,255,255,0.7); margin-top: 5px; }
        .status-card.warning .value { color: #fbbf24; }
        .status-card.critical .value { color: #f87171; }
        .section { 
            background: rgba(255,255,255,0.1); 
            backdrop-filter: blur(10px);
            border-radius: 15px; 
            padding: 25px; 
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .section h2 { color: white; margin-bottom: 20px; display: flex; align-items: center; }
        .section h2 .icon { margin-right: 10px; font-size: 1.2em; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid rgba(255,255,255,0.1); 
        }
        .table th { 
            background: rgba(255,255,255,0.1); 
            color: white; 
            font-weight: 600;
        }
        .table td { color: rgba(255,255,255,0.9); }
        .status-running { color: #4ade80; font-weight: bold; }
        .status-stopped { color: #f87171; font-weight: bold; }
        .btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }
        .btn:hover { background: rgba(255,255,255,0.3); transform: translateY(-1px); }
        .btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .btn.btn-success { background: rgba(34, 197, 94, 0.3); }
        .btn.btn-warning { background: rgba(251, 191, 36, 0.3); }
        .btn.btn-danger { background: rgba(248, 113, 113, 0.3); }
        .btn.btn-info { background: rgba(59, 130, 246, 0.3); }
        .actions { display: flex; gap: 8px; flex-wrap: wrap; }
        .health-healthy { color: #4ade80; }
        .health-warning { color: #fbbf24; }
        .health-critical { color: #f87171; }
        .performance-bar {
            width: 60px;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
        }
        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #fbbf24, #f87171);
            transition: width 0.3s ease;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }
        .modal-content {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            margin: 5% auto;
            padding: 20px;
            border-radius: 15px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .close {
            color: white;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover { opacity: 0.7; }
        .log-container {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            color: #e5e7eb;
        }
        .alert {
            padding: 12px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .alert-success { background: rgba(34, 197, 94, 0.1); border-color: #22c55e; }
        .alert-warning { background: rgba(251, 191, 36, 0.1); border-color: #fbbf24; }
        .alert-error { background: rgba(248, 113, 113, 0.1); border-color: #f87171; }
        .loading-spinner {
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top: 2px solid white;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 8px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .refresh-info { 
            text-align: center; 
            color: rgba(255,255,255,0.7); 
            margin-top: 20px; 
        }
        @keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.5; } }
        .loading { animation: pulse 2s infinite; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 Apple容器监控面板</h1>
            <p>实时监控和管理您的Apple原生容器服务</p>
            <div style="margin-top: 15px;">
                <button class="btn btn-success" onclick="managerAction('start')">🚀 启动所有服务</button>
                <button class="btn btn-warning" onclick="managerAction('restart')">🔄 重启所有服务</button>
                <button class="btn btn-danger" onclick="managerAction('stop')">⏹️ 停止所有服务</button>
                <button class="btn btn-info" onclick="showSystemInfo()">📊 系统信息</button>
            </div>
        </div>
        
        <div class="status-grid" id="statusGrid">
            <!-- 状态卡片将通过JavaScript填充 -->
        </div>
        
        <div class="section">
            <h2><span class="icon">📦</span>容器管理</h2>
            <div id="alertsContainer" style="margin-bottom: 15px;">
                <!-- 告警信息将通过JavaScript填充 -->
            </div>
            <table class="table" id="containersTable">
                <thead>
                    <tr>
                        <th>容器ID</th>
                        <th>镜像</th>
                        <th>状态</th>
                        <th>健康状态</th>
                        <th>IP地址</th>
                        <th>运行时间</th>
                        <th>CPU</th>
                        <th>内存</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="containersBody">
                    <!-- 容器数据将通过JavaScript填充 -->
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h2><span class="icon">💿</span>本地镜像</h2>
            <table class="table" id="imagesTable">
                <thead>
                    <tr>
                        <th>仓库</th>
                        <th>标签</th>
                        <th>镜像ID</th>
                        <th>大小</th>
                    </tr>
                </thead>
                <tbody id="imagesBody">
                    <!-- 镜像数据将通过JavaScript填充 -->
                </tbody>
            </table>
        </div>
        
        <div class="refresh-info">
            <p>页面每30秒自动刷新 | 上次更新: <span id="lastUpdate">--</span></p>
            <button class="btn" onclick="refreshData()" style="margin-top: 10px;">🔄 手动刷新</button>
        </div>
    </div>

    <!-- 日志查看模态框 -->
    <div id="logModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('logModal')">&times;</span>
            <h2>📋 容器日志</h2>
            <div style="margin: 15px 0;">
                <button class="btn btn-info" onclick="refreshLogs()">🔄 刷新日志</button>
                <select id="logLines" style="margin-left: 10px; padding: 5px; border-radius: 4px;">
                    <option value="50">最近50行</option>
                    <option value="100" selected>最近100行</option>
                    <option value="200">最近200行</option>
                    <option value="500">最近500行</option>
                </select>
            </div>
            <div id="logContent" class="log-container">
                正在加载日志...
            </div>
        </div>
    </div>

    <!-- 系统信息模态框 -->
    <div id="systemModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('systemModal')">&times;</span>
            <h2>📊 系统信息</h2>
            <div id="systemContent">
                正在加载系统信息...
            </div>
        </div>
    </div>

    <!-- 操作确认模态框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close" onclick="closeModal('confirmModal')">&times;</span>
            <h2>⚠️ 确认操作</h2>
            <p id="confirmMessage">确定要执行此操作吗？</p>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-danger" id="confirmBtn">确认</button>
                <button class="btn" onclick="closeModal('confirmModal')" style="margin-left: 10px;">取消</button>
            </div>
        </div>
    </div>

    <script>
        let refreshInterval;
        let currentContainerId = null;

        async function fetchData() {
            try {
                const response = await fetch('/api/data');
                const data = await response.json();

                if (data.error) {
                    showAlert('error', '数据获取失败: ' + data.error);
                    return;
                }

                updateDashboard(data);
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-CN');
            } catch (error) {
                console.error('获取数据失败:', error);
                showAlert('error', '网络连接失败，请检查服务器状态');
            }
        }

        function showAlert(type, message) {
            const alertsContainer = document.getElementById('alertsContainer');
            const alertClass = type === 'error' ? 'alert-error' : type === 'warning' ? 'alert-warning' : 'alert-success';

            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.innerHTML = `
                <strong>${type === 'error' ? '❌' : type === 'warning' ? '⚠️' : '✅'}</strong> ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; cursor: pointer;">×</button>
            `;

            alertsContainer.appendChild(alertDiv);

            // 5秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        function updateDashboard(data) {
            // 更新状态卡片
            const statusGrid = document.getElementById('statusGrid');
            const healthyContainers = data.containers.filter(c => c.health === 'healthy').length;
            const warningContainers = data.containers.filter(c => c.health === 'warning').length;
            const criticalContainers = data.containers.filter(c => c.health === 'critical').length;

            statusGrid.innerHTML = `
                <div class="status-card">
                    <h3>系统状态</h3>
                    <div class="value ${data.system_info.system_status === 'running' ? 'status-running' : 'status-stopped'}">
                        ${data.system_info.system_status === 'running' ? '🟢' : '🔴'}
                    </div>
                    <div class="label">${data.system_info.system_status === 'running' ? '运行中' : '已停止'}</div>
                </div>
                <div class="status-card">
                    <h3>运行容器</h3>
                    <div class="value">${data.system_info.running_containers}</div>
                    <div class="label">个容器在线</div>
                </div>
                <div class="status-card ${criticalContainers > 0 ? 'critical' : warningContainers > 0 ? 'warning' : ''}">
                    <h3>健康状态</h3>
                    <div class="value">${healthyContainers}/${data.containers.length}</div>
                    <div class="label">健康容器</div>
                </div>
                <div class="status-card">
                    <h3>镜像数量</h3>
                    <div class="value">${data.images.length}</div>
                    <div class="label">个镜像</div>
                </div>
            `;
            
            // 更新容器表格
            const containersBody = document.getElementById('containersBody');
            containersBody.innerHTML = data.containers.map(container => {
                const healthClass = `health-${container.health}`;
                const healthIcon = container.health === 'healthy' ? '💚' :
                                 container.health === 'warning' ? '💛' : '❤️';

                return `
                    <tr>
                        <td>${container.id}</td>
                        <td>${container.image}</td>
                        <td><span class="status-${container.state}">${container.state}</span></td>
                        <td><span class="${healthClass}">${healthIcon} ${container.health}</span></td>
                        <td>${container.addr}</td>
                        <td>${container.uptime}</td>
                        <td>
                            <div class="performance-bar">
                                <div class="performance-fill" style="width: ${container.performance.cpu_percent}%"></div>
                            </div>
                            <small>${container.performance.cpu_percent}%</small>
                        </td>
                        <td>
                            <div class="performance-bar">
                                <div class="performance-fill" style="width: ${container.performance.memory_percent}%"></div>
                            </div>
                            <small>${container.performance.memory_percent}%</small>
                        </td>
                        <td class="actions">
                            <button class="btn btn-info" onclick="showLogs('${container.id}')">📋 日志</button>
                            ${container.state === 'running' ?
                                `<button class="btn btn-warning" onclick="confirmAction('restart', '${container.id}')">🔄 重启</button>
                                 <button class="btn btn-danger" onclick="confirmAction('stop', '${container.id}')">⏹️ 停止</button>` :
                                `<button class="btn btn-success" onclick="confirmAction('start', '${container.id}')">▶️ 启动</button>`
                            }
                            <button class="btn btn-danger" onclick="confirmAction('delete', '${container.id}')">🗑️ 删除</button>
                        </td>
                    </tr>
                `;
            }).join('');
            
            // 更新镜像表格
            const imagesBody = document.getElementById('imagesBody');
            imagesBody.innerHTML = data.images.map(image => `
                <tr>
                    <td>${image.repository}</td>
                    <td>${image.tag}</td>
                    <td>${image.image_id.substring(0, 12)}...</td>
                    <td>${image.size}</td>
                </tr>
            `).join('');
        }
        
        async function containerAction(containerId, action) {
            try {
                showLoadingButton(true);

                const response = await fetch(`/api/container/${containerId}/action`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: action })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', `容器 ${containerId} ${action} 操作成功`);
                    // 刷新数据
                    setTimeout(fetchData, 1000);
                } else {
                    showAlert('error', `操作失败: ${result.message}`);
                }
            } catch (error) {
                console.error('容器操作失败:', error);
                showAlert('error', '操作失败，请检查网络连接');
            } finally {
                showLoadingButton(false);
            }
        }

        async function managerAction(action) {
            try {
                showLoadingButton(true);

                const response = await fetch(`/api/manager/${action}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', `管理操作 ${action} 执行成功`);
                    // 刷新数据
                    setTimeout(fetchData, 2000);
                } else {
                    showAlert('error', `操作失败: ${result.message}`);
                }
            } catch (error) {
                console.error('管理操作失败:', error);
                showAlert('error', '操作失败，请检查网络连接');
            } finally {
                showLoadingButton(false);
            }
        }

        function confirmAction(action, containerId) {
            const actionNames = {
                'start': '启动',
                'stop': '停止',
                'restart': '重启',
                'delete': '删除'
            };

            const message = `确定要${actionNames[action]}容器 ${containerId} 吗？`;
            document.getElementById('confirmMessage').textContent = message;

            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.onclick = () => {
                closeModal('confirmModal');
                containerAction(containerId, action);
            };

            showModal('confirmModal');
        }

        async function showLogs(containerId) {
            currentContainerId = containerId;
            showModal('logModal');
            await refreshLogs();
        }

        async function refreshLogs() {
            if (!currentContainerId) return;

            try {
                const lines = document.getElementById('logLines').value;
                const response = await fetch(`/api/container/${currentContainerId}/logs?lines=${lines}`);
                const result = await response.json();

                if (result.error) {
                    document.getElementById('logContent').textContent = `错误: ${result.error}`;
                } else {
                    document.getElementById('logContent').textContent = result.logs.join('\n');
                }
            } catch (error) {
                console.error('获取日志失败:', error);
                document.getElementById('logContent').textContent = '获取日志失败，请检查网络连接';
            }
        }

        function showSystemInfo() {
            showModal('systemModal');
            document.getElementById('systemContent').innerHTML = `
                <div class="log-container">
                    <h3>🍎 Apple容器系统信息</h3>
                    <p><strong>管理脚本:</strong> ${CONFIG?.container_manager_script || './apple-container-manager.sh'}</p>
                    <p><strong>刷新间隔:</strong> ${CONFIG?.refresh_interval || 30} 秒</p>
                    <p><strong>当前时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
                    <p><strong>浏览器:</strong> ${navigator.userAgent}</p>
                </div>
            `;
        }

        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function showLoadingButton(show) {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                if (show) {
                    btn.disabled = true;
                    if (!btn.querySelector('.loading-spinner')) {
                        btn.innerHTML = '<div class="loading-spinner"></div>' + btn.innerHTML;
                    }
                } else {
                    btn.disabled = false;
                    const spinner = btn.querySelector('.loading-spinner');
                    if (spinner) {
                        spinner.remove();
                    }
                }
            });
        }
        
        function refreshData() {
            const statusGrid = document.getElementById('statusGrid');
            if (statusGrid) {
                statusGrid.classList.add('loading');
            }
            fetchData().finally(() => {
                if (statusGrid) {
                    statusGrid.classList.remove('loading');
                }
            });
        }
        
        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', () => {
            fetchData();
            // 每30秒自动刷新
            refreshInterval = setInterval(fetchData, 30000);

            // 添加键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'r':
                            e.preventDefault();
                            refreshData();
                            break;
                        case 'l':
                            e.preventDefault();
                            if (currentContainerId) {
                                showLogs(currentContainerId);
                            }
                            break;
                    }
                }

                // ESC键关闭模态框
                if (e.key === 'Escape') {
                    const modals = document.querySelectorAll('.modal');
                    modals.forEach(modal => {
                        if (modal.style.display === 'block') {
                            modal.style.display = 'none';
                        }
                    });
                }
            });

            // 点击模态框外部关闭
            window.addEventListener('click', (e) => {
                if (e.target.classList.contains('modal')) {
                    e.target.style.display = 'none';
                }
            });
        });

        // 页面离开时清理定时器
        window.addEventListener('beforeunload', () => {
            if (refreshInterval) clearInterval(refreshInterval);
        });
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    """主页面"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/data')
def api_data():
    """API接口：获取所有数据"""
    try:
        data = {
            'containers': get_container_list(),
            'images': get_images_list(),
            'system_info': get_system_info(),
            'alerts': system_alerts[-10:],  # 最近10个告警
            'timestamp': datetime.now().isoformat()
        }
        return jsonify(data)
    except Exception as e:
        logger.error(f"获取数据失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/container/<container_id>/action', methods=['POST'])
def container_action(container_id):
    """容器操作API"""
    try:
        action = request.json.get('action')
        if not action:
            return jsonify({'error': '缺少操作参数'}), 400

        if action == 'start':
            stdout, stderr, code = run_command(f"container start {container_id}")
        elif action == 'stop':
            stdout, stderr, code = run_command(f"container stop {container_id}")
        elif action == 'restart':
            stdout, stderr, code = run_command(f"container restart {container_id}")
        elif action == 'delete':
            stdout, stderr, code = run_command(f"container delete {container_id}")
        else:
            return jsonify({'error': f'不支持的操作: {action}'}), 400

        result = {
            'success': code == 0,
            'message': stdout if code == 0 else stderr,
            'action': action,
            'container_id': container_id,
            'timestamp': datetime.now().isoformat()
        }

        if code == 0:
            logger.info(f"容器操作成功: {action} {container_id}")
        else:
            logger.error(f"容器操作失败: {action} {container_id}, 错误: {stderr}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"容器操作异常: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/container/<container_id>/logs')
def container_logs(container_id):
    """获取容器日志"""
    try:
        lines = request.args.get('lines', 100, type=int)
        stdout, stderr, code = run_command(f"container logs {container_id} --tail {lines}")

        if code != 0:
            return jsonify({'error': stderr}), 500

        return jsonify({
            'logs': stdout.split('\n'),
            'container_id': container_id,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"获取容器日志失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/manager/<action>', methods=['POST'])
def manager_action(action):
    """管理脚本操作API"""
    try:
        args = request.json.get('args', []) if request.json else []
        stdout, stderr, code = run_manager_script(action, *args)

        result = {
            'success': code == 0,
            'message': stdout if code == 0 else stderr,
            'action': action,
            'timestamp': datetime.now().isoformat()
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"管理脚本操作失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🍎 Apple容器Web监控面板")
    print("=" * 50)
    print("🚀 启动Web服务器...")
    print("📱 访问地址: http://localhost:5000")
    print("⚡ 功能特性:")
    print("   • 实时容器状态监控")
    print("   • 镜像管理界面")
    print("   • 自动刷新数据")
    print("   • 响应式设计")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}") 