#!/bin/bash

# Apple容器可视化界面启动脚本

echo "🍎 Apple容器可视化工具启动器"
echo "=================================="

# 检查依赖
check_dependencies() {
    echo "🔍 检查依赖..."
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 未安装"
        exit 1
    fi
    
    # 检查pip3
    if ! command -v pip3 &> /dev/null; then
        echo "❌ pip3 未安装"
        exit 1
    fi
    
    echo "✅ 基础依赖检查完成"
}

# 安装Python依赖
install_python_deps() {
    echo "📦 安装Python依赖..."
    
    # 检查并安装Flask
    if ! python3 -c "import flask" 2>/dev/null; then
        echo "🔄 安装Flask..."
        pip3 install flask --quiet
    fi
    
    # 检查并安装Rich (可选)
    if ! python3 -c "import rich" 2>/dev/null; then
        echo "🔄 安装Rich (终端美化)..."
        pip3 install rich --quiet || echo "⚠️  Rich安装失败，将使用简化界面"
    fi
    
    echo "✅ Python依赖安装完成"
}

# 显示菜单
show_menu() {
    echo ""
    echo "🎯 请选择可视化界面:"
    echo "1) 🌐 Web监控面板 (推荐) - http://localhost:5000"
    echo "2) 💻 终端仪表板 - 命令行界面"
    echo "3) 📊 简单状态检查"
    echo "4) 🔧 更新管理脚本"
    echo "5) ❌ 退出"
    echo ""
    read -p "请输入选项 (1-5): " choice
}

# 启动Web面板
start_web_panel() {
    echo ""
    echo "🌐 启动Web监控面板..."
    echo "=================================="
    echo "📱 访问地址: http://localhost:5000"
    echo "⚡ 功能特性:"
    echo "   • 实时容器状态监控"
    echo "   • 镜像管理界面"
    echo "   • 自动刷新数据"
    echo "   • 响应式设计"
    echo "=================================="
    echo "💡 提示: 按 Ctrl+C 停止服务"
    echo ""
    
    if [ -f "apple-container-web-panel.py" ]; then
        python3 apple-container-web-panel.py
    else
        echo "❌ 未找到 apple-container-web-panel.py"
        echo "请确保文件存在于当前目录"
    fi
}

# 启动终端仪表板
start_terminal_dashboard() {
    echo ""
    echo "💻 启动终端仪表板..."
    echo "=================================="
    
    if [ -f "apple-container-dashboard.py" ]; then
        python3 apple-container-dashboard.py
    else
        echo "❌ 未找到 apple-container-dashboard.py"
        echo "请确保文件存在于当前目录"
    fi
}

# 简单状态检查
simple_status() {
    echo ""
    echo "📊 Apple容器当前状态"
    echo "=================================="
    
    # 系统状态
    echo "🍎 系统状态:"
    if container system status >/dev/null 2>&1; then
        echo "   ✅ 容器系统运行正常"
    else
        echo "   ❌ 容器系统未运行"
        echo "   💡 运行: container system start"
    fi
    
    echo ""
    echo "📦 容器状态:"
    container list 2>/dev/null || echo "   ❌ 无法获取容器列表"
    
    echo ""
    echo "💿 镜像状态:"
    echo "   镜像数量: $(container images list 2>/dev/null | wc -l)"
    
    echo ""
    echo "🔗 服务访问地址:"
    ./apple-container-manager.sh status 2>/dev/null || echo "   ❌ 管理脚本未找到"
    
    echo ""
    echo "按回车键返回主菜单..."
    read
}

# 更新管理脚本
update_scripts() {
    echo ""
    echo "🔧 更新管理脚本..."
    echo "=================================="
    
    # 给脚本添加执行权限
    echo "📝 设置执行权限..."
    chmod +x apple-container-web-panel.py 2>/dev/null
    chmod +x apple-container-dashboard.py 2>/dev/null
    chmod +x apple-container-manager.sh 2>/dev/null
    chmod +x start-apple-container-ui.sh 2>/dev/null
    
    echo "✅ 权限设置完成"
    
    # 检查脚本状态
    echo ""
    echo "📋 脚本状态检查:"
    
    scripts=(
        "apple-container-web-panel.py:Web监控面板"
        "apple-container-dashboard.py:终端仪表板"
        "apple-container-manager.sh:容器管理器"
        "start-apple-container-ui.sh:UI启动器"
    )
    
    for script_info in "${scripts[@]}"; do
        script_name=$(echo $script_info | cut -d: -f1)
        script_desc=$(echo $script_info | cut -d: -f2)
        
        if [ -f "$script_name" ]; then
            echo "   ✅ $script_desc: $script_name"
        else
            echo "   ❌ $script_desc: $script_name (缺失)"
        fi
    done
    
    echo ""
    echo "✅ 脚本更新完成"
    echo "按回车键返回主菜单..."
    read
}

# 在后台启动Web面板
start_web_background() {
    echo "🌐 在后台启动Web面板..."
    
    if [ -f "apple-container-web-panel.py" ]; then
        nohup python3 apple-container-web-panel.py > web-panel.log 2>&1 &
        WEB_PID=$!
        echo "✅ Web面板已在后台启动 (PID: $WEB_PID)"
        echo "📱 访问地址: http://localhost:5000"
        echo "📋 日志文件: web-panel.log"
        echo "🛑 停止命令: kill $WEB_PID"
        echo ""
        echo "按回车键返回主菜单..."
        read
    else
        echo "❌ 未找到 apple-container-web-panel.py"
    fi
}

# 主循环
main() {
    # 检查依赖
    check_dependencies
    
    # 安装Python依赖
    install_python_deps
    
    while true; do
        clear
        echo "🍎 Apple容器可视化工具启动器"
        echo "=================================="
        echo "💡 为Apple原生容器提供可视化管理界面"
        
        show_menu
        
        case $choice in
            1)
                start_web_panel
                ;;
            2)
                start_terminal_dashboard
                ;;
            3)
                simple_status
                ;;
            4)
                update_scripts
                ;;
            5)
                echo ""
                echo "👋 再见!"
                exit 0
                ;;
            *)
                echo ""
                echo "❌ 无效选项，请重新选择"
                echo "按回车键继续..."
                read
                ;;
        esac
    done
}

# 运行主程序
main 