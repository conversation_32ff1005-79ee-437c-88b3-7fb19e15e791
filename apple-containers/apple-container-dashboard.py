#!/usr/bin/env python3
"""
Apple容器终端仪表板
使用Rich库创建美观的终端界面来监控Apple容器
"""
import subprocess
import time
import json
from datetime import datetime
import threading
import os

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.layout import Layout
    from rich.live import Live
    from rich.text import Text
    from rich.columns import Columns
    from rich.align import Align
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

console = Console()

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except subprocess.TimeoutExpired:
        return "", "Command timeout", 1
    except Exception as e:
        return "", str(e), 1

def get_container_list():
    """获取容器列表"""
    stdout, stderr, code = run_command("container list")
    if code != 0:
        return []
    
    containers = []
    lines = stdout.split('\n')[1:]  # 跳过标题行
    for line in lines:
        if line.strip():
            parts = line.split()
            if len(parts) >= 6:
                containers.append({
                    'id': parts[0],
                    'image': parts[1],
                    'os': parts[2],
                    'arch': parts[3],
                    'state': parts[4],
                    'addr': parts[5] if len(parts) > 5 else 'N/A'
                })
    return containers

def get_images_list():
    """获取镜像列表"""
    stdout, stderr, code = run_command("container images list")
    if code != 0:
        return []
    
    images = []
    lines = stdout.split('\n')[1:]
    for line in lines:
        if line.strip() and not line.startswith('REPOSITORY'):
            parts = line.split()
            if len(parts) >= 4:
                images.append({
                    'repository': parts[0],
                    'tag': parts[1],
                    'image_id': parts[2],
                    'size': parts[3] if len(parts) > 3 else 'N/A'
                })
    return images

def get_system_status():
    """获取系统状态"""
    stdout, stderr, code = run_command("container system status")
    return "🟢 运行中" if code == 0 else "🔴 已停止"

def create_status_panel():
    """创建状态面板"""
    containers = get_container_list()
    images = get_images_list()
    system_status = get_system_status()
    
    running_containers = len([c for c in containers if c['state'] == 'running'])
    
    status_table = Table.grid(padding=1)
    status_table.add_column(style="cyan", justify="right")
    status_table.add_column(style="magenta")
    
    status_table.add_row("🍎 系统状态:", system_status)
    status_table.add_row("📦 总容器数:", str(len(containers)))
    status_table.add_row("▶️  运行容器:", str(running_containers))
    status_table.add_row("💿 镜像数量:", str(len(images)))
    status_table.add_row("🕐 更新时间:", datetime.now().strftime('%H:%M:%S'))
    
    return Panel(
        Align.center(status_table),
        title="🍎 Apple容器状态",
        border_style="blue"
    )

def create_containers_table():
    """创建容器表格"""
    containers = get_container_list()
    
    table = Table(title="📦 容器列表", show_header=True, header_style="bold magenta")
    table.add_column("容器ID", style="cyan", no_wrap=True)
    table.add_column("镜像", style="green")
    table.add_column("状态", justify="center")
    table.add_column("IP地址", style="yellow")
    table.add_column("架构", justify="center")
    
    for container in containers:
        status_style = "green" if container['state'] == 'running' else "red"
        status_icon = "🟢" if container['state'] == 'running' else "🔴"
        
        table.add_row(
            container['id'],
            container['image'],
            f"{status_icon} {container['state']}",
            container['addr'],
            container['arch']
        )
    
    if not containers:
        table.add_row("", "无容器运行", "", "", "")
    
    return table

def create_images_table():
    """创建镜像表格"""
    images = get_images_list()
    
    table = Table(title="💿 镜像列表", show_header=True, header_style="bold cyan")
    table.add_column("仓库", style="blue")
    table.add_column("标签", style="green")
    table.add_column("镜像ID", style="yellow", no_wrap=True)
    table.add_column("大小", justify="right")
    
    for image in images:
        table.add_row(
            image['repository'],
            image['tag'],
            image['image_id'][:12] + '...' if len(image['image_id']) > 12 else image['image_id'],
            image['size']
        )
    
    if not images:
        table.add_row("无镜像", "", "", "")
    
    return table

def create_layout():
    """创建布局"""
    layout = Layout()
    
    layout.split_column(
        Layout(name="header", size=8),
        Layout(name="main", ratio=2),
        Layout(name="footer", size=3)
    )
    
    layout["main"].split_row(
        Layout(name="containers"),
        Layout(name="images")
    )
    
    return layout

def update_layout(layout):
    """更新布局内容"""
    # 头部状态面板
    layout["header"].update(create_status_panel())
    
    # 主要内容
    layout["containers"].update(create_containers_table())
    layout["images"].update(create_images_table())
    
    # 底部说明
    footer_content = Panel(
        Align.center(
            Text.from_markup(
                "[bold cyan]🍎 Apple容器监控仪表板[/bold cyan]\n"
                "[dim]每5秒自动刷新 | 按 Ctrl+C 退出[/dim]"
            )
        ),
        border_style="green"
    )
    layout["footer"].update(footer_content)

def run_dashboard():
    """运行仪表板"""
    if not RICH_AVAILABLE:
        print("❌ 需要安装Rich库: pip3 install rich")
        return
    
    console.print("\n🍎 [bold blue]Apple容器监控仪表板[/bold blue]")
    console.print("[dim]正在启动...[/dim]\n")
    
    layout = create_layout()
    
    try:
        with Live(layout, refresh_per_second=1, screen=True):
            while True:
                update_layout(layout)
                time.sleep(5)  # 每5秒刷新一次
    except KeyboardInterrupt:
        console.print("\n[yellow]👋 监控已停止[/yellow]")

def simple_dashboard():
    """简单文本仪表板（不需要Rich）"""
    print("🍎 Apple容器监控仪表板 (简化版)")
    print("=" * 60)
    
    try:
        while True:
            os.system('clear' if os.name == 'posix' else 'cls')
            
            print("🍎 Apple容器监控仪表板")
            print("=" * 60)
            
            # 系统状态
            containers = get_container_list()
            images = get_images_list()
            system_status = get_system_status()
            running_containers = len([c for c in containers if c['state'] == 'running'])
            
            print(f"\n📊 系统状态:")
            print(f"   系统: {system_status}")
            print(f"   容器: {running_containers}/{len(containers)} 运行中")
            print(f"   镜像: {len(images)} 个")
            print(f"   时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 容器列表
            print(f"\n📦 容器列表:")
            if containers:
                print(f"{'ID':<15} {'镜像':<30} {'状态':<10} {'IP地址':<15}")
                print("-" * 70)
                for container in containers:
                    status = "🟢 运行" if container['state'] == 'running' else "🔴 停止"
                    print(f"{container['id']:<15} {container['image']:<30} {status:<10} {container['addr']:<15}")
            else:
                print("   无容器运行")
            
            # 镜像列表
            print(f"\n💿 镜像列表:")
            if images:
                print(f"{'仓库':<25} {'标签':<10} {'大小':<10}")
                print("-" * 45)
                for image in images[:5]:  # 只显示前5个
                    print(f"{image['repository']:<25} {image['tag']:<10} {image['size']:<10}")
                if len(images) > 5:
                    print(f"   ... 还有 {len(images) - 5} 个镜像")
            else:
                print("   无镜像")
            
            print(f"\n按 Ctrl+C 退出 | 每5秒自动刷新")
            time.sleep(5)
            
    except KeyboardInterrupt:
        print("\n👋 监控已停止")

if __name__ == '__main__':
    if RICH_AVAILABLE:
        run_dashboard()
    else:
        print("⚠️  Rich库未安装，使用简化版界面")
        print("💡 安装Rich获得更好体验: pip3 install rich\n")
        simple_dashboard() 