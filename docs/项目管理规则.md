# 📋 Apple容器化项目管理规则

## 🎯 项目概述

本项目是一个**Apple容器化项目集合**，包含Apple容器管理工具、MCP代理服务和相关工具的完整解决方案。

## 📁 标准目录结构

```
📦 Apple容器化项目/
├── 🍎 apple-containers/          # Apple容器管理工具
│   ├── *.py                      # Python管理脚本
│   ├── *.sh                      # Shell管理脚本
│   └── *.yaml                    # 容器配置文件
├── 🔧 mcp-proxy/                 # MCP代理服务 (Go项目)
│   ├── *.go                      # Go源代码文件
│   ├── go.mod/go.sum             # Go模块依赖
│   ├── Dockerfile                # 容器构建文件
│   ├── build/                    # 构建产物 (gitignore)
│   └── docs/                     # 项目文档
├── 🛠️ mcp-tools/                 # MCP工具和配置
│   ├── *.json                    # MCP配置文件
│   ├── *.py                      # Python工具脚本
│   └── *.sh                      # Shell设置脚本
├── 📚 docs/                      # 项目文档中心
│   ├── *.md                      # Markdown文档
│   └── 项目管理规则.md            # 本文档
├── 🛠️ scripts/                   # 通用脚本工具
│   └── *.sh                      # Shell脚本
├── ⚙️ configs/                   # 全局配置文件
│   ├── global/                   # 全局配置
│   ├── development/              # 开发环境配置
│   └── production/               # 生产环境配置
├── 📁 data/                      # 数据存储目录
│   ├── logs/                     # 日志文件
│   ├── cache/                    # 缓存文件
│   └── temp/                     # 临时文件
├── 📁 archive/                   # 归档文件
│   └── *.yml                     # 历史配置文件
├── 🧪 tests/                     # 测试文件 (如需要)
│   ├── unit/                     # 单元测试
│   └── integration/              # 集成测试
└── 📄 README.md                  # 项目主说明文档
```

## 📝 文件命名规范

### 🐍 Python文件
- **脚本文件**: `kebab-case.py` (如: `apple-container-manager.py`)
- **模块文件**: `snake_case.py` (如: `config_manager.py`)
- **类文件**: `PascalCase.py` (如: `ContainerManager.py`)

### 🐚 Shell脚本
- **管理脚本**: `kebab-case.sh` (如: `docker-cleanup.sh`)
- **启动脚本**: `start-*.sh` (如: `start-apple-container-ui.sh`)
- **设置脚本**: `setup-*.sh` (如: `setup-mcp.sh`)

### 📄 配置文件
- **JSON配置**: `kebab-case.json` (如: `mcp-config.json`)
- **YAML配置**: `kebab-case.yaml` (如: `docker-compose.yaml`)
- **环境配置**: `.env.{environment}` (如: `.env.development`)

### 📚 文档文件
- **说明文档**: `PascalCase.md` (如: `Apple容器化完整指南.md`)
- **规范文档**: `kebab-case.md` (如: `coding-standards.md`)

## 🔧 开发规范

### 🐍 Python代码规范
```python
# 文件头部注释
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Apple容器管理工具
功能描述：提供Web界面管理Apple容器
作者：项目团队
创建时间：2024-XX-XX
"""

# 导入顺序：标准库 -> 第三方库 -> 本地模块
import os
import sys
from flask import Flask
from .config import settings
```

### 🐚 Shell脚本规范
```bash
#!/bin/bash
# Apple容器管理脚本
# 功能：管理Apple容器的启动、停止和状态查看
# 作者：项目团队
# 创建时间：2024-XX-XX

set -euo pipefail  # 严格模式

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color
```

### 🔧 Go代码规范
```go
// Package main provides MCP proxy functionality
package main

import (
    "context"
    "fmt"
    "log"
)

// Config represents the application configuration
type Config struct {
    Port     int    `json:"port"`
    Host     string `json:"host"`
    LogLevel string `json:"log_level"`
}
```

## 📦 依赖管理

### 🐍 Python项目
- 使用 `requirements.txt` 管理依赖
- 开发依赖使用 `requirements-dev.txt`
- 使用虚拟环境隔离依赖

### 🔧 Go项目
- 使用 `go.mod` 和 `go.sum` 管理依赖
- 定期运行 `go mod tidy` 清理依赖
- 使用 `go mod vendor` 创建vendor目录（可选）

## 🚀 构建和部署

### 🏗️ 构建规范
```bash
# Go项目构建
cd mcp-proxy
make build          # 本地构建
make docker-build   # Docker构建

# Python项目打包
cd apple-containers
pip install -r requirements.txt
python setup.py sdist bdist_wheel
```

### 🐳 容器化规范
- 使用多阶段构建减小镜像大小
- 设置合适的健康检查
- 使用非root用户运行容器
- 正确设置环境变量和配置

## 📊 版本管理

### 🏷️ 版本号规范
- 遵循语义化版本 (SemVer): `MAJOR.MINOR.PATCH`
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 🌿 分支管理
```
main/master     # 主分支，稳定版本
develop         # 开发分支
feature/*       # 功能分支
hotfix/*        # 热修复分支
release/*       # 发布分支
```

## 🧪 测试规范

### 📋 测试类型
- **单元测试**: 测试单个函数/方法
- **集成测试**: 测试组件间交互
- **端到端测试**: 测试完整用户流程

### 🎯 测试覆盖率
- 核心功能：≥90%
- 工具脚本：≥70%
- 配置文件：100%验证

## 📝 文档规范

### 📚 文档类型
1. **README.md**: 项目概述和快速开始
2. **API文档**: 接口说明和示例
3. **用户指南**: 详细使用说明
4. **开发文档**: 架构设计和开发指南
5. **部署文档**: 安装和配置说明

### ✍️ 文档写作规范
- 使用清晰的标题层级
- 提供代码示例和截图
- 包含故障排除部分
- 定期更新和维护

## 🔒 安全规范

### 🛡️ 代码安全
- 不在代码中硬编码密码和密钥
- 使用环境变量管理敏感信息
- 定期更新依赖包修复安全漏洞
- 进行代码安全扫描

### 🔐 配置安全
- 配置文件不包含敏感信息
- 使用加密存储敏感配置
- 设置合适的文件权限
- 定期轮换密钥和证书

## 📈 监控和日志

### 📊 监控指标
- 系统资源使用率
- 应用性能指标
- 错误率和响应时间
- 用户行为数据

### 📝 日志规范
```python
# Python日志示例
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data/logs/app.log'),
        logging.StreamHandler()
    ]
)
```

## 🔄 持续集成/持续部署 (CI/CD)

### 🚀 自动化流程
1. **代码提交** → 触发CI流程
2. **代码检查** → 静态分析和格式检查
3. **自动测试** → 运行测试套件
4. **构建打包** → 生成部署包
5. **自动部署** → 部署到目标环境

## 📞 问题反馈和支持

### 🐛 Bug报告
- 使用Issue模板报告问题
- 提供详细的复现步骤
- 包含环境信息和日志
- 标记优先级和类型

### 💡 功能请求
- 描述需求背景和目标
- 提供具体的使用场景
- 评估实现复杂度
- 讨论设计方案

---

## 📋 检查清单

### ✅ 代码提交前检查
- [ ] 代码格式化和静态检查通过
- [ ] 单元测试全部通过
- [ ] 文档已更新
- [ ] 变更日志已记录
- [ ] 安全检查通过

### ✅ 发布前检查
- [ ] 版本号已更新
- [ ] 发布说明已准备
- [ ] 备份已创建
- [ ] 回滚方案已准备
- [ ] 监控已配置

---

**📝 本文档将随项目发展持续更新，请定期查看最新版本。**

*最后更新：2024年7月*
