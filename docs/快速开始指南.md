# 🚀 MCP聚合器快速开始指南

## ✅ 已完成的设置

你现在拥有一个完整的MCP聚合器系统，可以解决IDE工具数量限制问题！

### 📦 已安装组件
- ✅ **combine-mcp v1.1.0** - MCP聚合器核心
- ✅ **mcp-switcher.py** - 智能场景切换器  
- ✅ **配置文件** - 支持多种开发场景
- ✅ **自动化脚本** - 一键设置和管理

## 🎯 立即使用（3步完成）

### 步骤1: 配置Cursor
复制以下内容到Cursor的MCP配置中：

```json
{
  "mcpServers": {
    "mcp-aggregator": {
      "command": "combine-mcp",
      "env": {
        "MCP_CONFIG": "/Users/<USER>/未命名文件夹/mcp-minimal.json",
        "MCP_LOG_LEVEL": "info",
        "MCP_CURSOR_MODE": "true"
      }
    }
  }
}
```

### 步骤2: 重启Cursor
重启Cursor后，你将看到聚合器提供的工具。

### 步骤3: 切换场景
根据需要切换到不同的开发场景：

```bash
# 查看所有可用场景
python3 mcp-switcher.py list

# 切换到Web开发场景（21个工具）
python3 mcp-switcher.py switch Web开发

# 自动根据关键词切换
python3 mcp-switcher.py auto 浏览器
```

## 📊 可用场景

| 场景名称 | 工具数量 | 适用场景 |
|----------|----------|----------|
| 通用开发 | 14个 | 日常编程、思考、文件操作 |
| Web开发 | 21个 | 前端开发、浏览器自动化 |
| GitHub协作 | 19个 | 版本控制、代码协作 |
| 知识管理 | 22个 | 笔记整理、数据库操作 |
| Web抓取 | 25个 | 数据采集、网页分析 |
| 全功能 | 34个 | 复杂项目的完整工具集 |

## 🔄 智能自动切换

系统会根据你的对话内容自动选择合适的场景：

- 提到"浏览器"、"网页" → 自动切换到Web开发
- 提到"GitHub"、"Git" → 自动切换到GitHub协作  
- 提到"Notion"、"笔记" → 自动切换到知识管理
- 提到"爬虫"、"抓取" → 自动切换到Web抓取

## 💡 常用命令

```bash
# 场景管理
python3 mcp-switcher.py list                    # 列出所有场景
python3 mcp-switcher.py switch "场景名"         # 手动切换
python3 mcp-switcher.py auto 关键词1 关键词2    # 自动切换

# 配置管理  
./setup-mcp.sh                                  # 重新运行设置
cp mcp-config-local.json mcp-config.json       # 使用本地配置
```

## 🎉 解决方案总结

**你的问题**: 100个MCP服务器 × 5个工具 = 500个工具，但IDE只支持40个

**我们的方案**: 
- ✅ 单一聚合器入口，无数量限制
- ✅ 智能场景切换，按需加载工具
- ✅ 工具过滤，精确控制暴露的功能
- ✅ 自动化管理，一键切换配置

现在你可以管理任意数量的MCP服务器，同时保持在IDE的工具限制范围内！

---

**需要帮助？** 查看 `MCP-聚合器使用指南.md` 获取详细说明。 