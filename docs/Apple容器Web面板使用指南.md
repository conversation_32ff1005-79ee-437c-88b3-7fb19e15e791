# 🌐 Apple容器Web监控面板使用指南

## 📖 概述

Apple容器Web监控面板是一个现代化的Web应用程序，为Apple原生容器提供完整的可视化管理和监控功能。通过直观的Web界面，您可以轻松管理容器生命周期、监控性能指标、查看日志等。

## 🚀 快速开始

### 启动Web面板

```bash
# 方法1: 使用启动器
cd apple-containers
./start-apple-container-ui.sh
# 选择 "1) 🌐 Web监控面板"

# 方法2: 直接启动
python3 apple-container-web-panel.py
```

### 访问Web面板

启动后，在浏览器中访问：
- **本地访问**: http://localhost:5000
- **网络访问**: http://[您的IP]:5000

## 🎯 主要功能

### 1. 📊 实时监控仪表板

**状态卡片显示**：
- 🍎 **系统状态**: 容器系统运行状态
- 📦 **运行容器**: 当前运行的容器数量
- 💚 **健康状态**: 健康容器数量统计
- 💿 **镜像数量**: 本地镜像总数

**颜色指示**：
- 🟢 绿色：正常状态
- 🟡 黄色：警告状态
- 🔴 红色：错误状态

### 2. 🔧 容器管理

**容器列表显示**：
- **容器ID**: 唯一标识符
- **镜像**: 使用的容器镜像
- **状态**: running/stopped
- **健康状态**: healthy/warning/critical
- **IP地址**: 容器网络地址
- **运行时间**: 容器运行时长
- **CPU使用率**: 实时CPU占用百分比
- **内存使用率**: 实时内存占用百分比

**容器操作**：
- 📋 **查看日志**: 实时日志流查看
- ▶️ **启动容器**: 启动已停止的容器
- ⏹️ **停止容器**: 停止运行中的容器
- 🔄 **重启容器**: 重启容器服务
- 🗑️ **删除容器**: 永久删除容器

### 3. 💿 镜像管理

**镜像信息显示**：
- **仓库名称**: 镜像仓库
- **标签**: 镜像版本标签
- **镜像ID**: 唯一标识符
- **大小**: 镜像文件大小

### 4. 📋 日志查看

**日志功能**：
- **实时查看**: 容器运行日志
- **行数选择**: 50/100/200/500行
- **自动刷新**: 手动刷新日志内容
- **全屏显示**: 模态框全屏查看

### 5. 🔧 系统管理

**批量操作**：
- 🚀 **启动所有服务**: 一键启动所有容器
- 🔄 **重启所有服务**: 批量重启容器
- ⏹️ **停止所有服务**: 批量停止容器
- 📊 **系统信息**: 查看详细系统信息

## 🎨 用户界面特性

### 现代化设计
- **毛玻璃效果**: 现代化视觉体验
- **渐变背景**: 美观的界面设计
- **响应式布局**: 完美适配各种屏幕尺寸
- **动画效果**: 流畅的交互动画

### 交互体验
- **实时更新**: 30秒自动刷新数据
- **操作反馈**: 实时操作状态提示
- **确认对话框**: 重要操作安全确认
- **加载指示器**: 操作进度可视化

### 快捷键支持
- **Ctrl/Cmd + R**: 手动刷新数据
- **Ctrl/Cmd + L**: 查看当前容器日志
- **ESC**: 关闭模态框

## 🚨 告警和通知

### 告警类型
- ✅ **成功**: 操作成功完成
- ⚠️ **警告**: 需要注意的状态
- ❌ **错误**: 操作失败或系统错误

### 健康监控
- **容器健康**: 自动检测容器健康状态
- **系统状态**: 监控容器系统运行状态
- **资源使用**: 监控CPU和内存使用情况

## 📱 移动端支持

### 响应式设计
- **自适应布局**: 自动适配手机和平板
- **触摸优化**: 优化的触摸交互
- **简化界面**: 移动端友好的界面布局

### 移动端功能
- **完整功能**: 支持所有桌面端功能
- **手势操作**: 支持滑动和点击操作
- **快速访问**: 移动端快捷操作

## 🔧 高级配置

### 配置选项
```python
CONFIG = {
    'refresh_interval': 30,          # 自动刷新间隔（秒）
    'max_log_lines': 1000,          # 最大日志行数
    'performance_history_size': 100, # 性能历史数据大小
    'container_manager_script': './apple-container-manager.sh'
}
```

### 自定义设置
- **刷新间隔**: 调整数据自动刷新频率
- **日志行数**: 设置日志显示的最大行数
- **历史数据**: 配置性能数据保存数量

## 🛠️ 故障排除

### 常见问题

**1. Web面板无法启动**
```bash
# 检查Python依赖
pip3 install flask

# 检查端口占用
lsof -i :5000
```

**2. 容器操作失败**
```bash
# 检查管理脚本权限
chmod +x apple-container-manager.sh

# 检查容器系统状态
container system status
```

**3. 数据显示异常**
- 检查容器系统是否正常运行
- 确认管理脚本路径正确
- 查看浏览器控制台错误信息

### 日志查看
```bash
# 查看Web面板日志
tail -f ../data/logs/web-panel.log

# 查看容器系统日志
container system logs
```

## 🔒 安全注意事项

### 网络安全
- **本地访问**: 默认仅允许本地访问
- **防火墙**: 配置适当的防火墙规则
- **HTTPS**: 生产环境建议使用HTTPS

### 操作安全
- **确认对话框**: 危险操作需要确认
- **操作日志**: 记录所有管理操作
- **权限控制**: 确保适当的文件权限

## 📈 性能优化

### 系统要求
- **Python**: 3.8+
- **内存**: 最少512MB可用内存
- **网络**: 稳定的网络连接

### 优化建议
- **定期清理**: 清理不需要的容器和镜像
- **监控资源**: 关注系统资源使用情况
- **日志轮转**: 定期清理旧日志文件

## 🆕 版本更新

### v2.0 新功能
- ✨ 实时容器操作功能
- 📊 性能监控和健康检查
- 📋 实时日志查看
- 🎨 全新UI设计
- 📱 移动端支持
- ⚡ 快捷键操作

### 升级说明
1. 备份现有配置
2. 更新Web面板文件
3. 重启Web服务
4. 验证功能正常

---

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **查看文档**: 首先查阅本使用指南
2. **检查日志**: 查看系统和应用日志
3. **重启服务**: 尝试重启Web面板服务
4. **系统检查**: 验证容器系统状态

**🎉 享受现代化的Apple容器管理体验！**
