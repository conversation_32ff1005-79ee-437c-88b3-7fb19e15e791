# MCP聚合器完整解决方案

## 🎯 解决的问题

你遇到的问题：**100个MCP服务器 × 5个工具 = 500个工具，但IDE只支持40个工具**

我们的解决方案：**MCP聚合器 + 智能场景切换**

## 📦 已安装的工具

1. **combine-mcp v1.1.0** - MCP聚合器核心
2. **mcp-switcher.py** - 智能场景切换器
3. **完整配置文件** - 支持10个主流MCP服务器

## 🚀 立即使用

### 第一步：在Cursor中配置MCP
将以下内容复制到Cursor的MCP配置中：

```json
{
  "mcpServers": {
    "mcp-aggregator": {
      "command": "combine-mcp",
      "env": {
        "MCP_CONFIG": "/Users/<USER>/未命名文件夹/mcp-config.json",
        "MCP_LOG_LEVEL": "info",
        "MCP_CURSOR_MODE": "true"
      }
    }
  }
}
```

### 第二步：重启Cursor
重启Cursor后，你将只看到1个MCP服务器（聚合器），但实际可以访问所有配置的工具。

### 第三步：根据需要切换场景

```bash
# 查看所有可用场景
python3 mcp-switcher.py list

# 切换到Web开发场景（21个工具）
python3 mcp-switcher.py switch Web开发

# 切换到知识管理场景（22个工具）
python3 mcp-switcher.py switch 知识管理

# 根据关键词自动选择
python3 mcp-switcher.py auto 浏览器 网页
```

## 📊 场景详情

| 场景 | 服务器数 | 工具数 | 适用情况 |
|------|----------|--------|----------|
| 通用开发 | 4 | 14 | 日常编程、思考、文件操作 |
| Web开发 | 6 | 21 | 前端开发、网页测试 |
| GitHub协作 | 5 | 19 | 代码协作、版本管理 |
| 知识管理 | 6 | 22 | 笔记整理、数据存储 |
| Web抓取 | 7 | 25 | 数据采集、网页分析 |
| 全功能 | 9 | 34 | 需要所有工具的复杂项目 |

## 🔧 高级配置

### 添加新的MCP服务器
编辑 `mcp-config.json`：

```json
{
  "mcpServers": {
    "新服务器": {
      "command": "npx",
      "args": ["-y", "@your/mcp-server"],
      "env": {
        "API_KEY": "your_api_key"
      },
      "tools": {
        "allowed": ["tool1", "tool2", "tool3"]
      }
    }
  }
}
```

### 创建自定义场景
编辑 `mcp-switcher.py` 中的 `scenarios` 字典：

```python
"我的场景": [
    "sequential-thinking", "memory", "filesystem", "新服务器"
]
```

### 环境变量配置
创建 `.env` 文件：

```bash
GITHUB_TOKEN=ghp_your_github_token
NOTION_API_KEY=ntn_your_notion_key
BRAVE_API_KEY=your_brave_key
```

## 🎨 工具名称映射

聚合器会自动为工具添加前缀：

| 原始名称 | 聚合后名称 |
|----------|------------|
| `navigate` | `playwright_navigate` |
| `search-stories` | `shortcut_search_stories` |
| `create_page` | `notion_create_page` |

## 🔄 自动切换触发词

| 关键词 | 自动切换到 |
|--------|------------|
| 浏览器、网页 | Web开发 |
| GitHub、Git | GitHub协作 |
| Notion、笔记 | 知识管理 |
| 爬虫、抓取 | Web抓取 |

## 🛠 故障排除

### 问题：工具超过40个限制
**解决方案**：使用更精确的场景或减少每个服务器的 `allowed` 工具列表

### 问题：某个MCP服务器启动失败
**解决方案**：检查环境变量和API密钥配置

### 问题：切换场景后工具没有更新
**解决方案**：重启Cursor或检查配置文件路径

## 📈 扩展到100个服务器

你可以按照以下模式扩展：

1. **分类管理**：按功能分组（AI、数据库、API、工具等）
2. **精确过滤**：每个服务器只暴露3-5个核心工具
3. **场景组合**：创建20-30个专门场景，每个场景15-35个工具
4. **智能路由**：根据项目类型自动选择最佳场景

这样你就可以管理100个MCP服务器，总共500个工具，但在任何时候只暴露需要的30-40个工具给IDE！

## 🎉 完成！

现在你拥有了一个完整的MCP管理系统，可以：
- ✅ 无限制添加MCP服务器
- ✅ 智能场景切换
- ✅ 工具数量精确控制
- ✅ 自动化配置管理
- ✅ 完整的备份和恢复

享受强大而灵活的MCP工具生态系统吧！🚀 