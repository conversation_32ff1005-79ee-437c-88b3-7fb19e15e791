2025-07-25 16:45:51,914 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-25 16:45:51,915 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-25 16:46:05,954 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:06,029 - __main__ - INFO - 执行命令: container images list
2025-07-25 16:46:06,055 - __main__ - INFO - 执行命令: container system status
2025-07-25 16:46:06,077 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:06,099 - __main__ - INFO - 执行命令: uptime
2025-07-25 16:46:06,109 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:46:06] "GET /api/data HTTP/1.1" 200 -
2025-07-25 16:46:22,277 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-07-25 16:46:22,277 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-25 16:46:34,929 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:34,960 - __main__ - INFO - 执行命令: container images list
2025-07-25 16:46:34,985 - __main__ - INFO - 执行命令: container system status
2025-07-25 16:46:35,007 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:35,028 - __main__ - INFO - 执行命令: uptime
2025-07-25 16:46:35,034 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:46:35] "GET /api/data HTTP/1.1" 200 -
2025-07-25 16:46:35,728 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:35,752 - __main__ - INFO - 执行命令: container images list
2025-07-25 16:46:35,774 - __main__ - INFO - 执行命令: container system status
2025-07-25 16:46:35,796 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:35,815 - __main__ - INFO - 执行命令: uptime
2025-07-25 16:46:35,821 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:46:35] "GET /api/data HTTP/1.1" 200 -
2025-07-25 16:46:35,946 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:35,968 - __main__ - INFO - 执行命令: container images list
2025-07-25 16:46:35,990 - __main__ - INFO - 执行命令: container system status
2025-07-25 16:46:36,011 - __main__ - INFO - 执行命令: container list
2025-07-25 16:46:36,030 - __main__ - INFO - 执行命令: uptime
2025-07-25 16:46:36,036 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:46:36] "GET /api/data HTTP/1.1" 200 -
2025-07-25 16:46:57,437 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:46:57] "GET / HTTP/1.1" 200 -
2025-07-25 16:47:06,909 - __main__ - INFO - 执行命令: container list
2025-07-25 16:47:06,976 - __main__ - INFO - 执行命令: container images list
2025-07-25 16:47:07,002 - __main__ - INFO - 执行命令: container system status
2025-07-25 16:47:07,025 - __main__ - INFO - 执行命令: container list
2025-07-25 16:47:07,048 - __main__ - INFO - 执行命令: uptime
2025-07-25 16:47:07,055 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:47:07] "GET /api/data HTTP/1.1" 200 -
2025-07-25 16:47:26,699 - __main__ - INFO - 执行命令: container list
2025-07-25 16:47:26,766 - __main__ - INFO - 执行命令: container images list
2025-07-25 16:47:26,789 - __main__ - INFO - 执行命令: container system status
2025-07-25 16:47:26,810 - __main__ - INFO - 执行命令: container list
2025-07-25 16:47:26,830 - __main__ - INFO - 执行命令: uptime
2025-07-25 16:47:26,837 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:47:26] "GET /api/data HTTP/1.1" 200 -
2025-07-25 16:48:37,684 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:48:37] "GET / HTTP/1.1" 200 -
2025-07-25 16:51:02,323 - __main__ - INFO - 执行命令: container list
2025-07-25 16:51:02,379 - __main__ - INFO - 执行命令: container images list
2025-07-25 16:51:02,403 - __main__ - INFO - 执行命令: container system status
2025-07-25 16:51:02,424 - __main__ - INFO - 执行命令: container list
2025-07-25 16:51:02,444 - __main__ - INFO - 执行命令: uptime
2025-07-25 16:51:02,450 - werkzeug - INFO - 127.0.0.1 - - [25/Jul/2025 16:51:02] "GET /api/data HTTP/1.1" 200 -
