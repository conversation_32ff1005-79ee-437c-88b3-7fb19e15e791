{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "tools": {"allowed": ["think", "analyze", "reasoning"]}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "tools": {"allowed": ["remember", "recall", "forget", "search_memory"]}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>"}, "tools": {"allowed": ["read_file", "write_file", "list_directory", "create_directory"]}}, "time": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "tools": {"allowed": ["get_current_time", "format_time", "time_zone"]}}, "playwright": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-playwright"], "tools": {"allowed": ["navigate", "screenshot", "click", "fill", "evaluate"]}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}, "tools": {"allowed": ["web_search", "news_search"]}}}}