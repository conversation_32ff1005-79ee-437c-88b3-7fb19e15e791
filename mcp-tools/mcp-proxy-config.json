{"mcpProxy": {"baseURL": "http://localhost:9090", "addr": ":9090", "name": "Local MCP Proxy", "version": "1.0.0", "type": "streamable-http", "options": {"panicIfInvalid": false, "logEnabled": true, "authTokens": []}}, "mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "options": {"panicIfInvalid": false, "logEnabled": true, "toolFilter": {"mode": "allow", "list": ["think", "analyze", "reason"]}}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "options": {"panicIfInvalid": false, "logEnabled": true, "toolFilter": {"mode": "allow", "list": ["remember", "recall", "search_memory"]}}}}}