#!/usr/bin/env python3
"""
MCP动态切换器
根据不同的开发场景自动调整MCP工具配置
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, List, Any

class MCPSwitcher:
    def __init__(self, config_dir: str = None):
        if config_dir is None:
            config_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_dir = Path(config_dir)
        self.base_config_file = self.config_dir / "mcp-config.json"
        self.cursor_config_file = self.config_dir / "cursor-mcp-config.json"
        
        # 定义不同场景的工具组合
        self.scenarios = {
            "通用开发": [
                "sequential-thinking", "memory", "filesystem", "time"
            ],
            "Web开发": [
                "sequential-thinking", "memory", "filesystem", "time", 
                "playwright", "brave-search"
            ],
            "GitHub协作": [
                "sequential-thinking", "memory", "filesystem", "time", 
                "github"
            ],
            "知识管理": [
                "sequential-thinking", "memory", "filesystem", "time", 
                "notion", "sqlite"
            ],
            "Web抓取": [
                "sequential-thinking", "memory", "filesystem", "time", 
                "playwright", "puppeteer", "brave-search"
            ],
            "全功能": [
                "sequential-thinking", "memory", "filesystem", "time", 
                "github", "notion", "playwright", "sqlite", "brave-search"
            ]
        }
    
    def load_base_config(self) -> Dict[str, Any]:
        """加载基础配置文件"""
        with open(self.base_config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def create_scenario_config(self, scenario: str) -> Dict[str, Any]:
        """根据场景创建配置"""
        if scenario not in self.scenarios:
            raise ValueError(f"未知场景: {scenario}. 可用场景: {list(self.scenarios.keys())}")
        
        base_config = self.load_base_config()
        selected_servers = self.scenarios[scenario]
        
        # 创建新的配置，只包含选定的服务器
        new_config = {"mcpServers": {}}
        for server_name in selected_servers:
            if server_name in base_config["mcpServers"]:
                new_config["mcpServers"][server_name] = base_config["mcpServers"][server_name]
        
        return new_config
    
    def switch_to_scenario(self, scenario: str):
        """切换到指定场景"""
        try:
            # 备份当前配置
            backup_file = self.config_dir / f"mcp-config-backup-{scenario}.json"
            if self.base_config_file.exists():
                shutil.copy2(self.base_config_file, backup_file)
            
            # 生成新配置
            new_config = self.create_scenario_config(scenario)
            
            # 写入新配置
            with open(self.base_config_file, 'w', encoding='utf-8') as f:
                json.dump(new_config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 已切换到场景: {scenario}")
            print(f"📊 当前启用的MCP服务器: {list(new_config['mcpServers'].keys())}")
            print(f"🔧 工具总数: {self.count_tools(new_config)}")
            
        except Exception as e:
            print(f"❌ 切换失败: {e}")
    
    def count_tools(self, config: Dict[str, Any]) -> int:
        """统计工具数量"""
        total_tools = 0
        for server_name, server_config in config["mcpServers"].items():
            if "tools" in server_config and "allowed" in server_config["tools"]:
                total_tools += len(server_config["tools"]["allowed"])
        return total_tools
    
    def list_scenarios(self):
        """列出所有可用场景"""
        print("🎯 可用的开发场景:")
        for scenario, servers in self.scenarios.items():
            config = self.create_scenario_config(scenario)
            tool_count = self.count_tools(config)
            print(f"  • {scenario}: {len(servers)}个服务器, {tool_count}个工具")
            print(f"    服务器: {', '.join(servers)}")
    
    def auto_switch_by_context(self, context_keywords: List[str]):
        """根据上下文关键词自动切换"""
        context_mapping = {
            "浏览器": "Web开发",
            "网页": "Web开发", 
            "爬虫": "Web抓取",
            "抓取": "Web抓取",
            "github": "GitHub协作",
            "git": "GitHub协作",
            "notion": "知识管理",
            "笔记": "知识管理",
            "数据库": "知识管理"
        }
        
        for keyword in context_keywords:
            if keyword.lower() in context_mapping:
                scenario = context_mapping[keyword.lower()]
                print(f"🤖 检测到关键词 '{keyword}', 自动切换到: {scenario}")
                self.switch_to_scenario(scenario)
                return
        
        print("🔄 未检测到特定场景，使用通用开发配置")
        self.switch_to_scenario("通用开发")

def main():
    import sys
    
    switcher = MCPSwitcher()
    
    if len(sys.argv) < 2:
        print("用法:")
        print("  python mcp-switcher.py list                    # 列出所有场景")
        print("  python mcp-switcher.py switch <场景名>         # 切换到指定场景")
        print("  python mcp-switcher.py auto <关键词1> [关键词2] # 根据关键词自动切换")
        return
    
    command = sys.argv[1]
    
    if command == "list":
        switcher.list_scenarios()
    elif command == "switch" and len(sys.argv) >= 3:
        scenario = sys.argv[2]
        switcher.switch_to_scenario(scenario)
    elif command == "auto" and len(sys.argv) >= 3:
        keywords = sys.argv[2:]
        switcher.auto_switch_by_context(keywords)
    else:
        print("❌ 无效的命令")

if __name__ == "__main__":
    main() 