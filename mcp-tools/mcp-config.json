{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "tools": {"allowed": ["think", "analyze", "reason"]}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "tools": {"allowed": ["remember", "recall", "forget", "search_memory"]}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>/未命名文件夹"}, "tools": {"allowed": ["read_file", "write_file", "list_directory", "create_directory"]}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {"DB_PATH": "/Users/<USER>/未命名文件夹/data/mcp.db"}, "tools": {"allowed": ["query", "execute", "list_tables"]}}}}