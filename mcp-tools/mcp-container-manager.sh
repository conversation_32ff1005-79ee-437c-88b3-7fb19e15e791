#!/bin/bash

# MCP容器管理脚本
# 管理需要容器化运行的MCP服务器

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose-mcp.yml"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker未运行或无法访问"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录..."
    mkdir -p data/{playwright,sqlite,puppeteer,docker,redis,postgres}
    log_success "数据目录创建完成"
}

# 检查需要容器化的MCP服务器
check_containerized_services() {
    echo "🐳 需要容器化的MCP服务器分析"
    echo "================================"
    
    local services=(
        "Playwright:浏览器自动化:高优先级"
        "Puppeteer:网页爬虫:高优先级"
        "SQLite:数据库隔离:中优先级"
        "Docker:容器管理:中优先级"
        "Redis:缓存服务:低优先级"
        "PostgreSQL:关系数据库:低优先级"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name desc priority <<< "$service"
        case $priority in
            "高优先级") color=$RED ;;
            "中优先级") color=$YELLOW ;;
            "低优先级") color=$GREEN ;;
        esac
        echo -e "${color}• $name${NC}: $desc ($priority)"
    done
    echo ""
}

# 启动容器化MCP服务
start_services() {
    log_info "启动容器化MCP服务..."
    check_docker
    create_directories
    
    if docker-compose -f "$COMPOSE_FILE" up -d; then
        log_success "MCP容器服务启动成功"
        log_info "等待服务初始化..."
        sleep 5
        show_status
    else
        log_error "MCP容器服务启动失败"
        exit 1
    fi
}

# 停止容器化MCP服务
stop_services() {
    log_info "停止容器化MCP服务..."
    if docker-compose -f "$COMPOSE_FILE" down; then
        log_success "MCP容器服务已停止"
    else
        log_error "停止MCP容器服务失败"
        exit 1
    fi
}

# 重启服务
restart_services() {
    log_info "重启MCP容器服务..."
    stop_services
    sleep 2
    start_services
}

# 显示服务状态
show_status() {
    echo ""
    echo "📊 MCP容器服务状态"
    echo "==================="
    docker-compose -f "$COMPOSE_FILE" ps
    echo ""
    
    echo "🔗 服务端点"
    echo "==========="
    echo "• MCP代理服务器: http://localhost:9090"
    echo "• Redis缓存: localhost:6379"
    echo "• PostgreSQL: localhost:5432"
    echo ""
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        log_info "显示所有服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs --tail=50 -f
    else
        log_info "显示 $service 服务日志..."
        docker-compose -f "$COMPOSE_FILE" logs --tail=50 -f "$service"
    fi
}

# 清理未使用的资源
cleanup() {
    log_info "清理MCP容器资源..."
    docker-compose -f "$COMPOSE_FILE" down --volumes --remove-orphans
    docker system prune -f --volumes
    log_success "清理完成"
}

# 检查特定服务
check_service() {
    local service=$1
    case $service in
        "playwright")
            echo "🎭 Playwright浏览器自动化服务"
            echo "用途: 网页自动化、截图、PDF生成"
            echo "需要容器化原因: 需要Chrome浏览器环境"
            ;;
        "puppeteer")
            echo "🕷️ Puppeteer网页爬虫服务"
            echo "用途: 网页数据抓取、动态内容获取"
            echo "需要容器化原因: Chrome浏览器依赖"
            ;;
        "sqlite")
            echo "🗄️ SQLite数据库服务"
            echo "用途: 轻量级数据存储"
            echo "建议容器化原因: 数据隔离和版本管理"
            ;;
        "docker")
            echo "🐳 Docker管理服务"
            echo "用途: 容器生命周期管理"
            echo "需要容器化原因: Docker-in-Docker模式"
            ;;
        *)
            log_error "未知服务: $service"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "MCP容器管理器 v1.0.0"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start           启动所有容器化MCP服务"
    echo "  stop            停止所有容器化MCP服务"
    echo "  restart         重启所有容器化MCP服务"
    echo "  status          显示服务状态"
    echo "  logs [service]  查看日志 (可选指定服务名)"
    echo "  cleanup         清理所有容器和数据"
    echo "  check           检查需要容器化的服务"
    echo "  service <name>  检查特定服务信息"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                 # 启动所有服务"
    echo "  $0 logs playwright       # 查看Playwright日志"
    echo "  $0 service puppeteer     # 检查Puppeteer服务信息"
}

# 主逻辑
case "${1:-help}" in
    "start")
        start_services
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs "$2"
        ;;
    "cleanup")
        cleanup
        ;;
    "check")
        check_containerized_services
        ;;
    "service")
        check_service "$2"
        ;;
    "help"|*)
        show_help
        ;;
esac 