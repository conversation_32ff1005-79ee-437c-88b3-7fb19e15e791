#!/bin/bash

echo "🚀 MCP聚合器自动设置脚本"
echo "=============================="

# 设置工作目录
WORK_DIR="/Users/<USER>/未命名文件夹"
cd "$WORK_DIR" || exit 1

# 检查combine-mcp是否已安装
if ! command -v combine-mcp &> /dev/null; then
    echo "❌ combine-mcp未安装，请先运行: curl -fsSL https://raw.githubusercontent.com/nazar256/combine-mcp/main/install.sh | bash"
    exit 1
fi

echo "✅ combine-mcp已安装"

# 创建数据目录
mkdir -p data
echo "✅ 数据目录已创建"

# 创建最小化配置
cat > mcp-minimal.json << 'EOF'
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "tools": {
        "allowed": ["remember", "recall", "search_memory"]
      }
    }
  }
}
EOF

echo "✅ 最小化配置已创建: mcp-minimal.json"

# 创建Cursor配置示例
cat > cursor-mcp-setup.json << 'EOF'
{
  "mcpServers": {
    "mcp-aggregator": {
      "command": "combine-mcp",
      "env": {
        "MCP_CONFIG": "/Users/<USER>/未命名文件夹/mcp-minimal.json",
        "MCP_LOG_LEVEL": "info",
        "MCP_CURSOR_MODE": "true"
      }
    }
  }
}
EOF

echo "✅ Cursor配置示例已创建: cursor-mcp-setup.json"

# 测试配置
echo "🧪 测试MCP聚合器配置..."
timeout 10s bash -c "MCP_CONFIG=$WORK_DIR/mcp-minimal.json combine-mcp" &
COMBINE_PID=$!
sleep 5

if kill -0 $COMBINE_PID 2>/dev/null; then
    echo "✅ MCP聚合器启动成功！"
    kill $COMBINE_PID 2>/dev/null
else
    echo "⚠️  MCP聚合器启动可能有问题，但配置文件已创建"
fi

echo ""
echo "📋 下一步操作："
echo "1. 将 cursor-mcp-setup.json 的内容复制到 Cursor 的 MCP 配置中"
echo "2. 重启 Cursor"
echo "3. 使用 python3 mcp-switcher.py 管理不同场景"
echo ""
echo "🎯 快速命令："
echo "  python3 mcp-switcher.py list                 # 查看所有场景"
echo "  python3 mcp-switcher.py switch 通用开发      # 切换场景"
echo "  python3 mcp-switcher.py auto 浏览器          # 自动切换"
echo ""
echo "🎉 MCP聚合器设置完成！" 