{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "tools": {"allowed": ["think", "analyze", "reasoning"]}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "tools": {"allowed": ["remember", "recall", "forget", "search_memory"]}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>"}, "tools": {"allowed": ["read_file", "write_file", "list_directory", "create_directory"]}}, "time": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "tools": {"allowed": ["get_current_time", "format_time", "time_zone"]}}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_TOKEN": "ghp_your_github_token_here"}, "tools": {"allowed": ["create_pr", "list_prs", "get_pr", "merge_pr", "search_repositories"]}}, "notion": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-notion"], "env": {"NOTION_API_KEY": "ntn_your_notion_api_key_here"}, "tools": {"allowed": ["create_page", "search_pages", "update_page", "get_page"]}}, "playwright": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-playwright"], "tools": {"allowed": ["navigate", "screenshot", "click", "fill", "evaluate"]}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {"DB_PATH": "./data/mcp.db"}, "tools": {"allowed": ["query", "execute", "create_table", "list_tables"]}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "tools": {"allowed": ["scrape", "pdf", "screenshot", "navigate"]}}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}, "tools": {"allowed": ["web_search", "news_search"]}}}}