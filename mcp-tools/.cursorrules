# MCP聚合器配置规则

## MCP工具管理策略

你现在使用的是MCP聚合器系统，可以智能管理多个MCP服务器而不受40个工具限制。

### 当前配置
- **聚合器**: combine-mcp v1.1.0
- **配置文件**: `/Users/<USER>/未命名文件夹/mcp-config.json`
- **切换工具**: `python3 mcp-switcher.py`

### 可用场景
1. **通用开发**: 14个工具 (思维、内存、文件系统、时间)
2. **Web开发**: 21个工具 (+ Playwright、搜索)
3. **GitHub协作**: 19个工具 (+ GitHub)
4. **知识管理**: 22个工具 (+ Notion、SQLite)
5. **Web抓取**: 25个工具 (+ Puppeteer)
6. **全功能**: 34个工具 (所有服务器)

### 自动切换规则
- 当用户提到"浏览器"、"网页" → 自动切换到Web开发
- 当用户提到"GitHub"、"Git" → 自动切换到GitHub协作
- 当用户提到"Notion"、"笔记" → 自动切换到知识管理
- 当用户提到"爬虫"、"抓取" → 自动切换到Web抓取

### 使用命令
```bash
# 列出所有场景
python3 mcp-switcher.py list

# 手动切换场景
python3 mcp-switcher.py switch "场景名"

# 根据关键词自动切换
python3 mcp-switcher.py auto 浏览器 网页
```

### 配置更新
- 修改 `mcp-config.json` 添加新的MCP服务器
- 修改 `mcp-switcher.py` 中的场景定义来调整工具组合
- 所有更改会自动通过聚合器生效，无需重启Cursor

### 注意事项
- 每次切换场景都会自动备份当前配置
- 工具名称会自动添加服务器前缀 (如: `playwright_navigate`)
- 环境变量和API密钥需要在各自的MCP服务器配置中设置 