{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>/未命名文件夹"}}, "time": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "${BRAVE_API_KEY}"}}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {"DB_PATH": "/Users/<USER>/未命名文件夹/data/mcp.db"}}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"}}, "playwright": {"command": "npx", "args": ["-y", "playwright-mcp"]}, "notion": {"command": "npx", "args": ["-y", "@suekou/mcp-notion-server"], "env": {"NOTION_API_KEY": "${NOTION_API_KEY}"}}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "${POSTGRES_CONNECTION_STRING}"}}, "docker": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-docker"]}, "google-drive": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdrive"], "env": {"GDRIVE_CLIENT_ID": "${GDRIVE_CLIENT_ID}", "GDRIVE_CLIENT_SECRET": "${GDRIVE_CLIENT_SECRET}", "GDRIVE_REFRESH_TOKEN": "${GDRIVE_REFRESH_TOKEN}"}}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}"}}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"]}, "everart": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everart"], "env": {"EVERART_API_KEY": "${EVERART_API_KEY}"}}, "aws-kb": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws-kb"], "env": {"AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}", "AWS_REGION": "${AWS_REGION}"}}, "youtube-transcript": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-youtube-transcript"]}, "obsidian": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-obsidian"], "env": {"OBSIDIAN_VAULT_PATH": "${OBSIDIAN_VAULT_PATH}"}}, "linear": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-linear"], "env": {"LINEAR_API_KEY": "${LINEAR_API_KEY}"}}}}