version: '3.8'

services:
  # MCP代理服务器
  mcp-proxy:
    image: ghcr.io/tbxark/mcp-proxy:latest
    ports:
      - "9090:9090"
    volumes:
      - ./mcp-proxy-config.json:/config/config.json:ro
    environment:
      - CONFIG_PATH=/config/config.json
    restart: unless-stopped
    networks:
      - mcp-network

  # Playwright浏览器自动化
  playwright-mcp:
    image: mcr.microsoft.com/playwright:latest
    command: ["npx", "@modelcontextprotocol/server-playwright"]
    volumes:
      - ./data/playwright:/data
    environment:
      - DISPLAY=:99
    networks:
      - mcp-network
    depends_on:
      - mcp-proxy

  # SQLite数据库
  sqlite-mcp:
    image: node:18-alpine
    working_dir: /app
    command: ["npx", "@modelcontextprotocol/server-sqlite"]
    volumes:
      - ./data/sqlite:/data
    environment:
      - DB_PATH=/data/mcp.db
    networks:
      - mcp-network

  # Puppeteer网页爬虫
  puppeteer-mcp:
    image: ghcr.io/puppeteer/puppeteer:latest
    command: ["npx", "@modelcontextprotocol/server-puppeteer"]
    volumes:
      - ./data/puppeteer:/data
    environment:
      - PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
      - PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable
    networks:
      - mcp-network

  # Docker管理服务
  docker-mcp:
    image: docker:latest
    command: ["npx", "docker-mcp-server"]
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./data/docker:/data
    privileged: true
    networks:
      - mcp-network

  # Redis缓存
  redis-mcp:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
    networks:
      - mcp-network

  # PostgreSQL数据库
  postgres-mcp:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=mcp_db
      - POSTGRES_USER=mcp_user
      - POSTGRES_PASSWORD=mcp_password
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  playwright-data:
  sqlite-data: 
  puppeteer-data:
  docker-data:
  redis-data:
  postgres-data: 