builds:
  - id: mcp-proxy
    main: .
    binary: mcp-proxy
    ldflags:
      - -s -w -X main.BuildVersion={{.Version}}
    goos:
      - linux
    goarch:
      - amd64
      - arm64

changelog:
  # Set it to true if you wish to skip the changelog generation.
  # This may result in an empty release notes on GitHub/GitLab/Gitea.
  disable: false

  # Changelog generation implementation to use.
  #
  # Valid options are:
  # - `git`: uses `git log`;
  # - `github`: uses the compare GitHub API, appending the author login to the changelog.
  # - `gitlab`: uses the compare GitLab API, appending the author name and email to the changelog.
  # - `github-native`: uses the GitHub release notes generation API, disables the groups feature.
  # - `gitea`: uses the compare Gitea API, appending the author login to the changelog.
  #
  # Defaults to `git`.
  use: github

  # Sorts the changelog by the commit's messages.
  # Could either be asc, desc or empty
  # Default is empty
  sort: asc

  # Group commits messages by given regex and title.
  # Order value defines the order of the groups.
  # Proving no regex means all commits will be grouped under the default group.
  # Groups are disabled when using github-native, as it already groups things by itself.
  #
  # Default is no groups.
  groups:
  groups:
    - title: Features
      regexp: '^feat(\([\w-]+\))?!?:.*'
      order: 0
    - title: "Bug Fixes"
      regexp: '^fix(\([\w-]+\))?!?:.*'
      order: 1
    - title: "Refactor"
      regexp: '^refactor(\([\w-]+\))?!?:.*'
      order: 2
    - title: "Build Process Updates"
      regexp: '^(build|ci)(\([\w-]+\))?!?:.*'
      order: 3
    - title: "Documentation Updates"
      regexp: '^docs(\([\w-]+\))?!?:.*'
      order: 4
    - title: "Maintenance"
      regexp: '^chore(\([\w-]+\))?!?:.*'
      order: 5
    - title: Others
      order: 999
