name: Goreleaser

on:
  push:
    tags:
      - "*"

env:
  GOPATH: /go_path
  GOCACHE: /go_cache

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup go
        uses: actions/setup-go@v5
        with:
          check-latest: true
          go-version: "^1"

      - name: cache go
        id: cache-go
        uses: actions/cache@v4
        with:
          path: |
            /go_path
            /go_cache
          key: go_path-${{ steps.hash-go.outputs.hash }}

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          # either 'goreleaser' (default) or 'goreleaser-pro'
          args: release --clean
        env:
          GITEA_TOKEN: ${{ secrets.GITHUB_TOKEN }}
