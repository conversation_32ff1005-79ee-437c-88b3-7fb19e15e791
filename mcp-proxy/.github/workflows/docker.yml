name: Build and Publish Docker Image

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Image Version'
        required: false
        default: 'latest'

jobs:
  build-and-push:
    permissions:
        packages: write
        contents: read
    runs-on: ubuntu-latest
    steps:
      - name: Build and push Docker image
        uses: TBXark/docker-action@master
        with:
          version: ${{ github.event.inputs.version || 'latest' }}
          docker_registry: ghcr.io
          docker_username: ${{ github.actor }}
          docker_password: ${{ secrets.GITHUB_TOKEN }}
          backup_registry: ${{ secrets.BACKUP_REGISTRY }}
          backup_username: ${{ secrets.BACKUP_USERNAME }}
          backup_password: ${{ secrets.BAC<PERSON>UP_PASSWORD }}
