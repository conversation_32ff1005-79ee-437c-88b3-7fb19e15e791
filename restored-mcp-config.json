{"mcpServers": {"sequential-thinking-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "逻辑思维增强MCP - 动态反思问题解决", "enabled": true}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "memory.json"}, "description": "持久化记忆存储MCP", "enabled": true}, "filesystem-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem"], "env": {"ROOT_PATH": "/Users/<USER>/未命名文件夹"}, "description": "文件系统操作MCP", "enabled": true}, "time-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "description": "时间和时区转换MCP", "enabled": true}, "wechat-service-mcp": {"command": "node", "args": ["/Users/<USER>/.cursor/servers/wechat-service-mcp.js"], "env": {"WECHAT_APPID": "wxa87c0181443d142f", "WECHAT_APPSECRET": "f239b47916a62c57fef6392957b65bf1", "WECHAT_TOKEN": "your-wechat-token", "WECHAT_ENCODING_AES_KEY": "your-encoding-aes-key"}, "description": "微信服务号MCP - 认证服务号完整功能（全局版本）", "enabled": true}, "notion-official": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-notion"], "env": {"NOTION_API_TOKEN": "${NOTION_TOKEN}"}, "description": "Notion官方MCP服务 - 知识库管理", "enabled": true}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_TOKEN}"}, "description": "GitHub仓库管理MCP", "enabled": true}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "${BRAVE_API_KEY}"}, "description": "Brave搜索引擎MCP - 网络和本地搜索服务", "enabled": true}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {"DB_PATH": "/Users/<USER>/未命名文件夹/data/mcp.db"}, "description": "SQLite数据库MCP - 数据库交互和商业智能", "enabled": true}, "playwright-mcp": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "env": {"PLAYWRIGHT_HEADLESS": "true", "PLAYWRIGHT_LAUNCH_OPTIONS": "{\"headless\": true, \"args\": [\"--no-sandbox\", \"--disable-setuid-sandbox\", \"--disable-dev-shm-usage\", \"--disable-gpu\", \"--no-first-run\", \"--no-default-browser-check\", \"--disable-extensions\", \"--disable-default-apps\"]}", "PLAYWRIGHT_BROWSER_TIMEOUT": "30000", "PLAYWRIGHT_NAVIGATION_TIMEOUT": "15000", "PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD": "false"}, "description": "Playwright自动化测试MCP - 优化配置防止浏览器卡死", "enabled": true}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "description": "Puppeteer浏览器自动化MCP - 网页抓取和自动化", "enabled": true}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "description": "HTTP客户端MCP - 外部API请求工具", "enabled": true}, "git-mcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"], "description": "Git版本控制MCP", "enabled": true}, "docker": {"command": "uvx", "args": ["docker-mcp"], "description": "Docker容器管理MCP", "enabled": true}, "aliyun-bailian": {"command": "node", "args": ["/Users/<USER>/.cursor/servers/aliyun-bailian-mcp.js"], "env": {"ALIYUN_BAILIAN_API_KEY": "${ALIYUN_BAILIAN_API_KEY}", "ALIYUN_ACCESS_KEY_ID": "${ALIYUN_ACCESS_KEY_ID}", "ALIYUN_ACCESS_KEY_SECRET": "${ALIYUN_ACCESS_KEY_SECRET}"}, "description": "阿里云百炼MCP - 通义千问AI模型服务（全局版本）", "enabled": true}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"DEFAULT_MINIMUM_TOKENS": "10000"}, "description": "Context7智能上下文管理MCP", "enabled": true}}}