<template>
  <div class="container-management">
    <a-layout>
      <a-layout-header class="header">
        <div class="header-content">
          <h1><AppleOutlined /> Apple容器管理面板</h1>
          <p>现代化的容器管理界面</p>
        </div>
      </a-layout-header>
      
      <a-layout-content class="content">
        <!-- 统计卡片 -->
        <div class="stats-row">
          <a-card class="stats-card">
            <Statistic
              title="运行容器"
              :value="systemInfo.containerCount"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <ContainerOutlined />
              </template>
            </Statistic>
          </a-card>
          
          <a-card class="stats-card">
            <Statistic
              title="可用镜像"
              :value="systemInfo.imageCount"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <PictureOutlined />
              </template>
            </Statistic>
          </a-card>
          
          <a-card class="stats-card">
            <Statistic
              title="系统版本"
              :value="systemInfo.version"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <SettingOutlined />
              </template>
            </Statistic>
          </a-card>
        </div>

        <!-- 容器列表 -->
        <a-card title="容器列表" class="container-card">
          <a-table
            :columns="containerColumns"
            :data-source="containerList"
            :loading="loading"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button size="small" @click="viewLogs(record)">
                    <FileTextOutlined /> 日志
                  </a-button>
                  <a-button size="small" type="primary" @click="startContainer(record)">
                    <PlayCircleOutlined /> 启动
                  </a-button>
                  <a-button size="small" danger @click="stopContainer(record)">
                    <StopOutlined /> 停止
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 镜像列表 -->
        <a-card title="镜像列表" class="image-card">
          <a-table
            :columns="imageColumns"
            :data-source="imageList"
            :loading="loading"
            :pagination="false"
            size="small"
          />
        </a-card>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  Layout, 
  Card, 
  Table, 
  Tag, 
  Button, 
  Space, 
  Statistic,
  ConfigProvider 
} from 'ant-design-vue'
import {
  AppleOutlined,
  ContainerOutlined,
  PictureOutlined,
  SettingOutlined,
  FileTextOutlined,
  PlayCircleOutlined,
  StopOutlined
} from '@ant-design/icons-vue'
import { getContainerList, getImageList, getSystemInfo } from '../api/container'

const { Header, Content } = Layout

const loading = ref(false)
const containerList = ref([])
const imageList = ref([])
const systemInfo = ref({
  containerCount: 0,
  imageCount: 0,
  version: 'Unknown'
})

const containerColumns = [
  {
    title: '容器名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '端口',
    dataIndex: 'ports',
    key: 'ports',
  },
  {
    title: '操作',
    key: 'action',
  },
]

const imageColumns = [
  {
    title: '镜像名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '标签',
    dataIndex: 'tag',
    key: 'tag',
  },
  {
    title: '大小',
    dataIndex: 'size',
    key: 'size',
  },
]

const getStatusColor = (status: string) => {
  const colorMap = {
    running: 'green',
    stopped: 'red',
    paused: 'orange',
  }
  return colorMap[status] || 'default'
}

const loadData = async () => {
  loading.value = true
  try {
    const [containers, images, system] = await Promise.all([
      getContainerList(),
      getImageList(),
      getSystemInfo(),
    ])
    containerList.value = containers
    imageList.value = images
    systemInfo.value = system
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const viewLogs = (container: any) => {
  console.log('查看日志:', container.name)
}

const startContainer = (container: any) => {
  console.log('启动容器:', container.name)
}

const stopContainer = (container: any) => {
  console.log('停止容器:', container.name)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.container-management {
  min-height: 100vh;
  background: #f0f2f5;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0 50px;
  height: auto;
  line-height: 1.5;
}

.header-content {
  text-align: center;
  color: white;
  padding: 20px 0;
}

.header-content h1 {
  margin: 0;
  font-size: 2em;
  font-weight: 700;
}

.header-content p {
  margin: 10px 0 0 0;
  opacity: 0.9;
}

.content {
  padding: 24px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stats-card {
  text-align: center;
}

.container-card,
.image-card {
  margin-bottom: 24px;
}

.ant-card-head {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ant-card-head-title {
  color: white;
}
</style>
