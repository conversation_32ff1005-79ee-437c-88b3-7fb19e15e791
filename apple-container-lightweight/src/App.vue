<template>
  <div id="app">
    <a-config-provider>
      <ContainerManagement />
    </a-config-provider>
  </div>
</template>

<script setup lang="ts">
import { ConfigProvider } from 'ant-design-vue'
import ContainerManagement from './components/ContainerManagement.vue'
</script>

<style>
#app {
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
