<template>
  <div class="container-management">
    <PageWrapper title="🍎 Apple容器管理">
      <div class="container-grid">
        <!-- 容器列表 -->
        <Card title="容器列表" class="container-list">
          <Table
            :columns="containerColumns"
            :dataSource="containerList"
            :loading="loading"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <Tag :color="getStatusColor(record.status)">
                  {{ record.status }}
                </Tag>
              </template>
              <template v-else-if="column.key === 'action'">
                <Space>
                  <Button size="small" @click="viewLogs(record)">日志</Button>
                  <Button size="small" @click="viewStats(record)">统计</Button>
                </Space>
              </template>
            </template>
          </Table>
        </Card>

        <!-- 系统信息 -->
        <Card title="系统信息" class="system-info">
          <Descriptions :column="1" size="small">
            <DescriptionsItem label="容器数量">{{ systemInfo.containerCount }}</DescriptionsItem>
            <DescriptionsItem label="镜像数量">{{ systemInfo.imageCount }}</DescriptionsItem>
            <DescriptionsItem label="系统版本">{{ systemInfo.version }}</DescriptionsItem>
          </Descriptions>
        </Card>
      </div>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Card, Table, Tag, Button, Space, Descriptions, DescriptionsItem } from 'ant-design-vue';
import { PageWrapper } from '/@/components/Page';
import { getContainerList, getSystemInfo } from '/@/api/container';

const loading = ref(false);
const containerList = ref([]);
const systemInfo = ref({
  containerCount: 0,
  imageCount: 0,
  version: '',
});

const containerColumns = [
  {
    title: '容器名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '端口',
    dataIndex: 'ports',
    key: 'ports',
  },
  {
    title: '操作',
    key: 'action',
  },
];

const getStatusColor = (status: string) => {
  const colorMap = {
    running: 'green',
    stopped: 'red',
    paused: 'orange',
  };
  return colorMap[status] || 'default';
};

const loadData = async () => {
  loading.value = true;
  try {
    const [containers, system] = await Promise.all([
      getContainerList(),
      getSystemInfo(),
    ]);
    containerList.value = containers;
    systemInfo.value = system;
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    loading.value = false;
  }
};

const viewLogs = (container: any) => {
  console.log('查看日志:', container.name);
};

const viewStats = (container: any) => {
  console.log('查看统计:', container.name);
};

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.container-management {
  padding: 16px;
}

.container-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 16px;
}

.container-list {
  min-height: 400px;
}

.system-info {
  height: fit-content;
}
</style>
