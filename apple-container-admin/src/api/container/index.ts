import { defHttp } from '/@/utils/http/axios';

enum Api {
  ContainerList = '/api/containers',
  ContainerLogs = '/api/containers/logs',
  ContainerStats = '/api/containers/stats',
  ImageList = '/api/images',
  SystemInfo = '/api/system',
}

export const getContainerList = () => {
  return defHttp.get({ url: Api.ContainerList });
};

export const getContainerLogs = (containerId: string) => {
  return defHttp.get({ url: Api.ContainerLogs, params: { containerId } });
};

export const getContainerStats = (containerId: string) => {
  return defHttp.get({ url: Api.ContainerStats, params: { containerId } });
};

export const getImageList = () => {
  return defHttp.get({ url: Api.ImageList });
};

export const getSystemInfo = () => {
  return defHttp.get({ url: Api.SystemInfo });
};
