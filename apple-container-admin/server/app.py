#!/usr/bin/env python3
"""
Apple容器API服务
为Vben Admin提供后端API支持
"""
import subprocess
import json
import time
from flask import Flask, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

def run_command(cmd):
    """执行shell命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except Exception as e:
        return "", str(e), 1

@app.route('/api/containers', methods=['GET'])
def get_containers():
    """获取容器列表"""
    stdout, stderr, code = run_command('container list --format json')
    if code == 0:
        try:
            containers = json.loads(stdout)
            return jsonify(containers)
        except:
            return jsonify([])
    return jsonify([])

@app.route('/api/images', methods=['GET'])
def get_images():
    """获取镜像列表"""
    stdout, stderr, code = run_command('container images list --format json')
    if code == 0:
        try:
            images = json.loads(stdout)
            return jsonify(images)
        except:
            return jsonify([])
    return jsonify([])

@app.route('/api/system', methods=['GET'])
def get_system_info():
    """获取系统信息"""
    # 获取容器数量
    stdout, stderr, code = run_command('container list --format json')
    container_count = 0
    if code == 0:
        try:
            containers = json.loads(stdout)
            container_count = len(containers)
        except:
            pass
    
    # 获取镜像数量
    stdout, stderr, code = run_command('container images list --format json')
    image_count = 0
    if code == 0:
        try:
            images = json.loads(stdout)
            image_count = len(images)
        except:
            pass
    
    # 获取版本信息
    stdout, stderr, code = run_command('container version')
    version = stdout if code == 0 else "Unknown"
    
    return jsonify({
        'containerCount': container_count,
        'imageCount': image_count,
        'version': version,
        'timestamp': time.time()
    })

@app.route('/api/containers/logs', methods=['GET'])
def get_container_logs():
    """获取容器日志"""
    container_id = request.args.get('containerId')
    if not container_id:
        return jsonify({'error': 'Container ID required'}), 400
    
    stdout, stderr, code = run_command(f'container logs {container_id}')
    return jsonify({
        'logs': stdout,
        'error': stderr
    })

@app.route('/api/containers/stats', methods=['GET'])
def get_container_stats():
    """获取容器统计信息"""
    container_id = request.args.get('containerId')
    if not container_id:
        return jsonify({'error': 'Container ID required'}), 400
    
    stdout, stderr, code = run_command(f'container stats {container_id}')
    return jsonify({
        'stats': stdout,
        'error': stderr
    })

if __name__ == '__main__':
    print("🍎 Apple容器API服务启动中...")
    print("📡 API地址: http://localhost:5000")
    print("🔗 前端地址: http://localhost:3100")
    app.run(host='0.0.0.0', port=5000, debug=False)
