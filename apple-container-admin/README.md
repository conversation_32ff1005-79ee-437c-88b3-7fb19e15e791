# 🍎 Apple容器可视化界面

基于Vben Admin构建的现代化Apple容器管理界面

## 🚀 快速启动

```bash
# 启动所有服务
./start-apple-admin.sh
```

## 📡 服务地址

- **前端界面**: http://localhost:3100
- **后端API**: http://localhost:5000

## 🎯 功能特性

- ✅ 容器列表管理
- ✅ 实时状态监控
- ✅ 日志查看
- ✅ 系统信息展示
- ✅ 响应式设计
- ✅ 现代化UI

## 🛠️ 技术栈

- **前端**: Vue 3 + TypeScript + Ant Design Vue
- **后端**: Python Flask
- **构建**: Vite
- **UI**: Ant Design Vue

## 📱 界面预览

- 容器管理面板
- 系统信息展示
- 实时数据更新
- 现代化设计风格

## 🔧 自定义配置

编辑 `src/views/container/index.vue` 自定义界面
编辑 `server/app.py` 自定义API接口
