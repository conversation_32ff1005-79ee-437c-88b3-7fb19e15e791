#!/bin/bash

echo "🍎 Apple容器可视化界面启动脚本"
echo "================================"

# 检查Python依赖
echo "📦 检查Python依赖..."
python3 -c "import flask" 2>/dev/null || {
    echo "📦 安装Flask..."
    python3 -m pip install --break-system-packages flask flask-cors
}

# 启动后端API服务
echo "🚀 启动后端API服务..."
cd server
python3 app.py &
API_PID=$!
cd ..

# 等待API服务启动
sleep 3

# 启动前端服务
echo "🚀 启动前端服务..."
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ 服务启动完成！"
echo "📡 后端API: http://localhost:5000"
echo "🌐 前端界面: http://localhost:3100"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $API_PID $FRONTEND_PID 2>/dev/null; exit" INT
wait
