#!/usr/bin/env python3
"""
🍎 Apple容器可视化界面 - 简化版
基于Flask + Bootstrap的现代化容器管理界面
开箱即用，零配置
"""

import subprocess
import json
import time
from datetime import datetime
from flask import Flask, render_template_string, jsonify, request
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

def run_command(cmd):
    """执行shell命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except Exception as e:
        return "", str(e), 1

def get_container_list():
    """获取容器列表"""
    stdout, stderr, code = run_command('container list --format json')
    if code == 0:
        try:
            containers = json.loads(stdout)
            return containers
        except:
            return []
    return []

def get_image_list():
    """获取镜像列表"""
    stdout, stderr, code = run_command('container images list --format json')
    if code == 0:
        try:
            images = json.loads(stdout)
            return images
        except:
            return []
    return []

def get_system_info():
    """获取系统信息"""
    containers = get_container_list()
    images = get_image_list()
    
    stdout, stderr, code = run_command('container version')
    version = stdout if code == 0 else "Unknown"
    
    return {
        'containerCount': len(containers),
        'imageCount': len(images),
        'version': version,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍎 Apple容器管理面板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px;
            padding: 30px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        .stats-card h3 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .stats-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .container-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .container-card .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 20px;
        }
        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
        }
        .status-running {
            background: #d4edda;
            color: #155724;
        }
        .status-stopped {
            background: #f8d7da;
            color: #721c24;
        }
        .status-paused {
            background: #fff3cd;
            color: #856404;
        }
        .btn-action {
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 0.9em;
            font-weight: 600;
            border: none;
            margin: 2px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            font-size: 1.5em;
            transition: transform 0.3s ease;
        }
        .refresh-btn:hover {
            transform: rotate(180deg);
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="bi bi-apple"></i> Apple容器管理面板</h1>
                <p>现代化的容器管理界面，实时监控和管理您的Apple容器</p>
            </div>

            <!-- 统计信息 -->
            <div class="row" id="stats">
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3 id="containerCount">-</h3>
                        <p><i class="bi bi-box"></i> 运行容器</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3 id="imageCount">-</h3>
                        <p><i class="bi bi-image"></i> 可用镜像</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3 id="systemVersion">-</h3>
                        <p><i class="bi bi-gear"></i> 系统版本</p>
                    </div>
                </div>
            </div>

            <!-- 容器列表 -->
            <div class="container-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> 容器列表</h5>
                </div>
                <div class="card-body">
                    <div id="containerList" class="loading">
                        <i class="bi bi-arrow-clockwise"></i> 加载中...
                    </div>
                </div>
            </div>

            <!-- 镜像列表 -->
            <div class="container-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-images"></i> 镜像列表</h5>
                </div>
                <div class="card-body">
                    <div id="imageList" class="loading">
                        <i class="bi bi-arrow-clockwise"></i> 加载中...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 刷新按钮 -->
    <button class="refresh-btn" onclick="loadData()">
        <i class="bi bi-arrow-clockwise"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function getStatusClass(status) {
            const statusMap = {
                'running': 'status-running',
                'stopped': 'status-stopped',
                'paused': 'status-paused'
            };
            return statusMap[status] || 'status-stopped';
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function renderContainers(containers) {
            const containerList = document.getElementById('containerList');
            
            if (containers.length === 0) {
                containerList.innerHTML = '<p class="text-muted text-center">暂无容器</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-hover">';
            html += '<thead><tr><th>容器名称</th><th>状态</th><th>端口</th><th>创建时间</th><th>操作</th></tr></thead><tbody>';
            
            containers.forEach(container => {
                html += `<tr>
                    <td><strong>${container.name || container.id}</strong></td>
                    <td><span class="status-badge ${getStatusClass(container.status)}">${container.status}</span></td>
                    <td>${container.ports || '-'}</td>
                    <td>${formatDate(container.created)}</td>
                    <td>
                        <button class="btn btn-action btn-primary" onclick="viewLogs('${container.id}')">
                            <i class="bi bi-file-text"></i> 日志
                        </button>
                        <button class="btn btn-action btn-success" onclick="startContainer('${container.id}')">
                            <i class="bi bi-play"></i> 启动
                        </button>
                        <button class="btn btn-action btn-danger" onclick="stopContainer('${container.id}')">
                            <i class="bi bi-stop"></i> 停止
                        </button>
                    </td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
            containerList.innerHTML = html;
        }

        function renderImages(images) {
            const imageList = document.getElementById('imageList');
            
            if (images.length === 0) {
                imageList.innerHTML = '<p class="text-muted text-center">暂无镜像</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-hover">';
            html += '<thead><tr><th>镜像名称</th><th>标签</th><th>大小</th><th>创建时间</th></tr></thead><tbody>';
            
            images.forEach(image => {
                html += `<tr>
                    <td><strong>${image.name || image.id}</strong></td>
                    <td><span class="badge bg-secondary">${image.tag || 'latest'}</span></td>
                    <td>${image.size || '-'}</td>
                    <td>${formatDate(image.created)}</td>
                </tr>`;
            });
            
            html += '</tbody></table></div>';
            imageList.innerHTML = html;
        }

        async function loadData() {
            try {
                // 加载系统信息
                const systemResponse = await fetch('/api/system');
                const systemData = await systemResponse.json();
                
                document.getElementById('containerCount').textContent = systemData.containerCount;
                document.getElementById('imageCount').textContent = systemData.imageCount;
                document.getElementById('systemVersion').textContent = systemData.version;

                // 加载容器列表
                const containersResponse = await fetch('/api/containers');
                const containersData = await containersResponse.json();
                renderContainers(containersData);

                // 加载镜像列表
                const imagesResponse = await fetch('/api/images');
                const imagesData = await imagesResponse.json();
                renderImages(imagesData);

            } catch (error) {
                console.error('加载数据失败:', error);
                document.getElementById('containerList').innerHTML = 
                    '<div class="error">加载数据失败，请检查Apple容器服务是否正常运行</div>';
            }
        }

        function viewLogs(containerId) {
            alert('查看日志功能: ' + containerId);
        }

        function startContainer(containerId) {
            alert('启动容器: ' + containerId);
        }

        function stopContainer(containerId) {
            alert('停止容器: ' + containerId);
        }

        // 页面加载完成后自动加载数据
        document.addEventListener('DOMContentLoaded', loadData);

        // 每30秒自动刷新一次
        setInterval(loadData, 30000);
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    """主页面"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/containers')
def api_containers():
    """获取容器列表API"""
    containers = get_container_list()
    return jsonify(containers)

@app.route('/api/images')
def api_images():
    """获取镜像列表API"""
    images = get_image_list()
    return jsonify(images)

@app.route('/api/system')
def api_system():
    """获取系统信息API"""
    system_info = get_system_info()
    return jsonify(system_info)

@app.route('/api/containers/<container_id>/logs')
def api_container_logs(container_id):
    """获取容器日志API"""
    stdout, stderr, code = run_command(f'container logs {container_id}')
    return jsonify({
        'logs': stdout,
        'error': stderr
    })

@app.route('/api/containers/<container_id>/start', methods=['POST'])
def api_start_container(container_id):
    """启动容器API"""
    stdout, stderr, code = run_command(f'container start {container_id}')
    return jsonify({
        'success': code == 0,
        'output': stdout,
        'error': stderr
    })

@app.route('/api/containers/<container_id>/stop', methods=['POST'])
def api_stop_container(container_id):
    """停止容器API"""
    stdout, stderr, code = run_command(f'container stop {container_id}')
    return jsonify({
        'success': code == 0,
        'output': stdout,
        'error': stderr
    })

if __name__ == '__main__':
    print("🍎 Apple容器可视化界面启动中...")
    print("=" * 50)
    print("🌐 访问地址: http://localhost:5000")
    print("⚡ 功能特性:")
    print("   • 现代化UI设计")
    print("   • 实时数据更新")
    print("   • 容器管理功能")
    print("   • 响应式设计")
    print("   • 开箱即用")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}") 