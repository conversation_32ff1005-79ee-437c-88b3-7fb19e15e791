# 🎨 Figma MCP 设置指南

## 📋 **配置状态**

✅ **已添加到配置文件**: `complete-50-mcp-config.json`
✅ **服务器数量**: 53个MCP服务器
✅ **Figma MCP**: 已启用

## 🔧 **Figma MCP 配置详情**

```json
"figma": {
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-figma"],
  "env": {
    "FIGMA_ACCESS_TOKEN": "${FIGMA_ACCESS_TOKEN}",
    "FIGMA_FILE_KEY": "${FIGMA_FILE_KEY}"
  },
  "description": "Figma设计协作MCP - 设计稿管理和代码生成",
  "enabled": true
}
```

## 🔑 **需要配置的环境变量**

### 1. **FIGMA_ACCESS_TOKEN**
- 获取地址: https://www.figma.com/developers/api#access-tokens
- 用途: 访问Figma API的认证令牌

### 2. **FIGMA_FILE_KEY**
- 获取方式: 从Figma文件URL中提取
- 格式: `https://www.figma.com/file/XXXXX/...` 中的 `XXXXX` 部分

## 🚀 **快速设置步骤**

### 步骤1: 获取Figma Access Token
1. 访问 https://www.figma.com/developers/api#access-tokens
2. 登录Figma账户
3. 创建新的Personal Access Token
4. 复制生成的token

### 步骤2: 获取File Key
1. 打开你的Figma设计文件
2. 复制浏览器地址栏的URL
3. 提取文件ID (URL中的 `file/` 后面的部分)

### 步骤3: 设置环境变量
```bash
# 添加到你的环境变量文件
export FIGMA_ACCESS_TOKEN="your_figma_access_token_here"
export FIGMA_FILE_KEY="your_figma_file_key_here"
```

## 🎯 **Figma MCP 功能特性**

### ✅ **核心功能**
- 📁 **文件管理** - 获取和更新Figma文件
- 🎨 **设计提取** - 提取设计元素和样式
- 💻 **代码生成** - 从设计稿生成Vue/React组件
- 🔄 **同步更新** - 设计变更自动同步

### ✅ **适用场景**
- 前端组件开发
- 设计系统构建
- UI/UX设计协作
- 原型到代码转换

## 📝 **使用示例**

### 1. **获取设计文件信息**
```
@figma get_file_info
```

### 2. **提取组件样式**
```
@figma get_component_styles
```

### 3. **生成Vue组件**
```
@figma generate_vue_component
```

## 🔗 **相关链接**

- [Figma API 文档](https://www.figma.com/developers/api)
- [Figma MCP 服务器](https://github.com/modelcontextprotocol/server-figma)
- [Cursor MCP 配置](https://cursor.sh/docs/mcp)

## ⚠️ **注意事项**

1. **权限设置** - 确保Access Token有足够的权限
2. **文件访问** - 确保可以访问目标Figma文件
3. **API限制** - 注意Figma API的请求限制
4. **安全考虑** - 不要将Access Token提交到代码仓库

## 🎉 **完成状态**

- ✅ Figma MCP已添加到配置文件
- ✅ 配置格式正确
- ✅ 已启用状态
- ⏳ 需要配置环境变量才能使用

**下一步**: 配置FIGMA_ACCESS_TOKEN和FIGMA_FILE_KEY环境变量 